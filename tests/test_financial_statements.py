"""
Comprehensive tests for financial statement generation.
"""

from datetime import datetime
from mcx3d_finance.core.financials.balance_sheet import BalanceSheetGenerator

class TestFinancialStatements:
    """Test financial statement generation."""

    def test_balance_sheet_generation(self, db_session, sample_organization):
        """Test balance sheet generation with NASDAQ compliance."""
        generator = BalanceSheetGenerator(db_session)

        balance_sheet = generator.generate_balance_sheet(
            sample_organization.id,
            datetime.utcnow(),
            include_comparative=True
        )

        # Verify structure
        assert 'header' in balance_sheet
        assert 'assets' in balance_sheet
        assert 'liabilities_and_equity' in balance_sheet
        assert 'financial_analysis' in balance_sheet

        # Verify NASDAQ compliance
        assert balance_sheet['header']['statement_title'] == 'CONSOLIDATED BALANCE SHEETS'
        assert 'amounts_in' in balance_sheet['header']
        assert 'currency' in balance_sheet['header']

        # Verify balance equation
        total_assets = balance_sheet['assets']['total_assets']['current']
        total_liab_equity = balance_sheet['liabilities_and_equity']['total_liabilities_and_equity']['current']
        assert abs(total_assets - total_liab_equity) < 0.01  # Allow for rounding

    def test_balance_sheet_with_detailed_accounts(self, db_session, detailed_organization_with_transactions):
        """
        Test balance sheet with a detailed chart of accounts and transactions to verify classification.
        """
        org_id = detailed_organization_with_transactions
        generator = BalanceSheetGenerator(db_session)

        report_date = datetime(2023, 12, 31)
        balance_sheet = generator.generate_balance_sheet(org_id, report_date)

        # Detailed Assertions for Assets
        assets = balance_sheet['assets']
        assert 'total_current_assets' in assets
        assert 'total_non_current_assets' in assets
        assert 'total_assets' in assets

        # Verify key accounts are present (based on a standard chart of accounts)
        assert 'cash_and_cash_equivalents' in assets['current_assets']
        assert 'accounts_receivable_net' in assets['current_assets']
        assert 'property_plant_and_equipment_net' in assets['non_current_assets']

        # Check that subtotals are calculated
        assert assets['total_current_assets']['current'] > 0
        assert assets['total_non_current_assets']['current'] > 0

        # Detailed Assertions for Liabilities and Equity
        liab_equity = balance_sheet['liabilities_and_equity']
        assert 'total_current_liabilities' in liab_equity
        assert 'total_non_current_liabilities' in liab_equity
        assert 'total_liabilities' in liab_equity
        assert 'total_equity' in liab_equity

        # Verify key accounts
        assert 'accounts_payable' in liab_equity['current_liabilities']
        assert 'long_term_debt' in liab_equity['non_current_liabilities']
        assert 'retained_earnings' in liab_equity['equity']

        # Check that subtotals are calculated
        assert liab_equity['total_current_liabilities']['current'] > 0
        assert liab_equity['total_non_current_liabilities']['current'] > 0

        # Verify the main equation again with detailed data
        total_assets = assets['total_assets']['current']
        total_liab_equity = liab_equity['total_liabilities_and_equity']['current']

        calculated_liab_equity = liab_equity['total_liabilities']['current'] + liab_equity['total_equity']['current']

        assert abs(total_liab_equity - calculated_liab_equity) < 0.01
        assert abs(total_assets - total_liab_equity) < 0.01
