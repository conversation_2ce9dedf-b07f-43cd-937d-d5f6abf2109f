# MCX3D Financials API - Xero Import Endpoints

This document describes the new Xero data import API endpoints that have been added to the MCX3D Financials application.

## Overview

The Xero Import API provides endpoints to:
- Import data from Xero (full or incremental)
- Check Xero authentication status
- Monitor sync status and progress
- Trigger background sync tasks

All endpoints require authentication and return JSON responses.

## Authentication

All endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Endpoints

### 1. Import Xero Data

**POST** `/api/v1/xero/import`

Import data from Xero for a specific organization.

**Request Body:**
```json
{
  "organization_id": 2,
  "incremental": false,
  "force_refresh": false
}
```

**Parameters:**
- `organization_id` (int, required): The ID of the organization to import data for
- `incremental` (bool, optional): If true, only import changes since last sync. Default: false
- `force_refresh` (bool, optional): If true, force refresh of all data. Default: false

**Response:**
```json
{
  "success": true,
  "organization_id": 2,
  "import_stats": {},
  "processed_data_summary": {},
  "timestamp": "2025-07-22T15:45:00.000Z"
}
```

**Status Codes:**
- `200`: Import started successfully
- `404`: Organization not found
- `409`: Import already in progress
- `401`: Authentication required
- `500`: Server error

### 2. Check Xero Authentication Status

**GET** `/api/v1/xero/auth-status/{organization_id}`

Check the Xero authentication status for an organization.

**Path Parameters:**
- `organization_id` (int): The organization ID

**Response:**
```json
{
  "organization_id": 2,
  "is_authenticated": true,
  "expires_at": "2025-07-23T15:45:00.000Z",
  "scopes": ["accounting.transactions", "accounting.contacts.read"],
  "tenant_name": "Modular CX"
}
```

### 3. Get Sync Status

**GET** `/api/v1/xero/sync-status/{organization_id}`

Get the current sync status for an organization.

**Path Parameters:**
- `organization_id` (int): The organization ID

**Response:**
```json
{
  "organization_id": 2,
  "sync_status": "completed",
  "last_sync_at": "2025-07-22T14:30:00.000Z",
  "next_sync_at": "2025-07-23T14:30:00.000Z",
  "records_synced": {
    "accounts": {"total": 107, "imported": 107, "errors": 0},
    "contacts": {"total": 374, "imported": 374, "errors": 0},
    "invoices": {"total": 120, "imported": 120, "errors": 0},
    "transactions": {"total": 4851, "imported": 4851, "errors": 0}
  },
  "errors": []
}
```

**Possible sync_status values:**
- `never_synced`: No sync has been performed
- `running`: Sync is currently in progress
- `completed`: Last sync completed successfully
- `failed`: Last sync failed with errors

### 4. Trigger Sync Task

**POST** `/api/v1/xero/sync/{organization_id}`

Trigger a sync using the Celery task system (recommended for production).

**Path Parameters:**
- `organization_id` (int): The organization ID

**Query Parameters:**
- `force_full` (bool, optional): If true, perform full sync instead of incremental. Default: false

**Response:**
```json
{
  "task_id": "celery-task-uuid-123",
  "organization_id": 2,
  "status": "queued",
  "message": "Sync task has been queued and will start shortly"
}
```

### 5. Get Task Status

**GET** `/api/v1/xero/task-status/{task_id}`

Get the status of a Celery task.

**Path Parameters:**
- `task_id` (string): The Celery task ID

**Response:**
```json
{
  "task_id": "celery-task-uuid-123",
  "status": "SUCCESS",
  "result": {
    "success": true,
    "organization_id": 2,
    "records_processed": 5452
  },
  "info": null
}
```

**Possible task status values:**
- `PENDING`: Task is waiting to be processed
- `STARTED`: Task has been started
- `SUCCESS`: Task completed successfully  
- `FAILURE`: Task failed
- `RETRY`: Task is being retried
- `REVOKED`: Task was revoked

## Usage Examples

### Python (using requests)

```python
import requests

# Authentication
headers = {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
}

# Start import
response = requests.post(
    'http://localhost:8000/api/v1/xero/import',
    json={
        'organization_id': 2,
        'incremental': False,
        'force_refresh': False
    },
    headers=headers
)

# Check sync status
status_response = requests.get(
    'http://localhost:8000/api/v1/xero/sync-status/2',
    headers=headers
)

print(f"Import response: {response.json()}")
print(f"Sync status: {status_response.json()}")
```

### curl

```bash
# Set your JWT token
TOKEN="your_jwt_token_here"

# Start import
curl -X POST "http://localhost:8000/api/v1/xero/import" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"organization_id": 2, "incremental": false}'

# Check auth status
curl -X GET "http://localhost:8000/api/v1/xero/auth-status/2" \
     -H "Authorization: Bearer $TOKEN"

# Check sync status
curl -X GET "http://localhost:8000/api/v1/xero/sync-status/2" \
     -H "Authorization: Bearer $TOKEN"

# Trigger sync task
curl -X POST "http://localhost:8000/api/v1/xero/sync/2?force_full=false" \
     -H "Authorization: Bearer $TOKEN"
```

## Error Handling

All endpoints return structured error responses:

```json
{
  "detail": "Organization 999 not found",
  "error": "Not Found",
  "timestamp": "2025-07-22T15:45:00.000Z",
  "request_id": "req-123"
}
```

Common error scenarios:
- **401 Unauthorized**: Invalid or missing JWT token
- **403 Forbidden**: User doesn't have access to the organization
- **404 Not Found**: Organization not found
- **409 Conflict**: Import already in progress
- **500 Internal Server Error**: Server-side error during import

## Best Practices

1. **Use the Celery task endpoint** (`/sync/{organization_id}`) for production imports as it handles long-running operations better.

2. **Check auth status first** before attempting imports to ensure Xero authentication is valid.

3. **Monitor task progress** using the task status endpoint when using Celery tasks.

4. **Handle rate limits** - Xero has API rate limits, so avoid triggering multiple imports simultaneously.

5. **Use incremental sync** when possible to reduce API calls and improve performance.

6. **Check sync status** to avoid triggering imports when one is already running.

## Development Notes

- The API uses FastAPI with automatic OpenAPI documentation available at `/docs`
- Background tasks use Celery for reliable processing
- All times are in UTC ISO format
- The API supports CORS for local development
- Rate limiting is implemented to prevent abuse