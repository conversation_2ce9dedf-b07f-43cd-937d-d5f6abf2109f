"""
MCX3D Finance CLI - Command Line Interface for financial data management.
"""

import click
import logging
from .data import sync
from .reports import generate
from .valuation import valuate
from .analytics import analytics

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

@click.group()
@click.option('--debug/--no-debug', default=False, help='Enable debug logging')
def cli(debug):
    """MCX3D Finance CLI - Financial data management and reporting tool.
    
    Features:
    - sync: Synchronize data from Xero
    - generate: Generate financial reports (income statement, balance sheet, cash flow)
    - valuate: Run valuation models (DCF, multiples, SaaS) with professional PDF/Excel exports
    - analytics: Calculate SaaS KPIs and business metrics
    
    Professional Report Exports:
    Use 'valuate' commands with --export pdf/excel for investor-grade valuation reports.
    """
    if debug:
        logging.getLogger().setLevel(logging.DEBUG)
        click.echo('Debug mode enabled')


# Register command groups
cli.add_command(sync)
cli.add_command(generate)
cli.add_command(valuate)
cli.add_command(analytics)


if __name__ == '__main__':
    cli()