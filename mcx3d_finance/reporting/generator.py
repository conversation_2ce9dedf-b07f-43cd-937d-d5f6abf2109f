"""
Enhanced report generator with PDF and Excel output capabilities.
"""

import logging
from typing import Dict, Any, List

# PDF generation
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import (
    SimpleDocTemplate,
    Table,
    TableStyle,
    Paragraph,
    Spacer,
    PageBreak,
)
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT

# Excel generation
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment

logger = logging.getLogger(__name__)


class ReportGenerator:
    """Enhanced report generator with multiple output formats."""

    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()

    def _setup_custom_styles(self):
        """Setup custom styles for PDF generation."""
        # Company header style
        self.styles.add(
            ParagraphStyle(
                name="CompanyHeader",
                parent=self.styles["Heading1"],
                fontSize=16,
                spaceAfter=6,
                alignment=TA_CENTER,
                textColor=colors.black,
            )
        )

        # Statement title style
        self.styles.add(
            ParagraphStyle(
                name="StatementTitle",
                parent=self.styles["Heading2"],
                fontSize=14,
                spaceAfter=12,
                alignment=TA_CENTER,
                textColor=colors.black,
            )
        )

        # Section header style
        self.styles.add(
            ParagraphStyle(
                name="SectionHeader",
                parent=self.styles["Heading3"],
                fontSize=12,
                spaceBefore=12,
                spaceAfter=6,
                textColor=colors.black,
            )
        )

        # Financial data style
        self.styles.add(
            ParagraphStyle(
                name="FinancialData",
                parent=self.styles["Normal"],
                fontSize=10,
                alignment=TA_RIGHT,
            )
        )

    def generate_balance_sheet_pdf(
        self, balance_sheet_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant balance sheet PDF."""
        try:
            logger.info(f"Generating balance sheet PDF: {output_path}")

            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )

            story = []

            # Header information
            header = balance_sheet_data.get("header", {})
            story.append(
                Paragraph(
                    header.get("company_name", "Company Name"),
                    self.styles["CompanyHeader"],
                )
            )
            story.append(
                Paragraph(
                    header.get("statement_title", "BALANCE SHEET"),
                    self.styles["StatementTitle"],
                )
            )
            story.append(
                Paragraph(
                    f"As of {header.get('reporting_date', 'Date')}",
                    self.styles["Normal"],
                )
            )

            if header.get("comparative_date"):
                story.append(
                    Paragraph(
                        f"(With comparative figures as of {header.get('comparative_date')})",
                        self.styles["Normal"],
                    )
                )

            story.append(
                Paragraph(
                    f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')})",
                    self.styles["Normal"],
                )
            )
            story.append(Spacer(1, 20))

            # Assets section
            story.append(Paragraph("ASSETS", self.styles["SectionHeader"]))
            assets_table_data = self._build_balance_sheet_assets_table(
                balance_sheet_data.get("assets", {})
            )
            assets_table = self._create_financial_table(assets_table_data)
            story.append(assets_table)
            story.append(Spacer(1, 20))

            # Liabilities and Equity section
            story.append(
                Paragraph(
                    "LIABILITIES AND STOCKHOLDERS' EQUITY", self.styles["SectionHeader"]
                )
            )
            liab_equity_table_data = self._build_balance_sheet_liabilities_table(
                balance_sheet_data.get("liabilities_and_equity", {})
            )
            liab_equity_table = self._create_financial_table(liab_equity_table_data)
            story.append(liab_equity_table)
            story.append(Spacer(1, 20))

            # Financial analysis
            if "financial_analysis" in balance_sheet_data:
                story.append(PageBreak())
                story.append(
                    Paragraph("FINANCIAL ANALYSIS", self.styles["SectionHeader"])
                )
                analysis_table_data = self._build_financial_analysis_table(
                    balance_sheet_data["financial_analysis"]
                )
                analysis_table = self._create_financial_table(analysis_table_data)
                story.append(analysis_table)

            # Compliance certifications
            if "compliance" in balance_sheet_data:
                story.append(Spacer(1, 20))
                story.append(
                    Paragraph("COMPLIANCE CERTIFICATIONS", self.styles["SectionHeader"])
                )
                for cert in balance_sheet_data["compliance"].get("certifications", []):
                    story.append(Paragraph(f"• {cert}", self.styles["Normal"]))
                    story.append(Spacer(1, 6))

            doc.build(story)
            logger.info("Balance sheet PDF generated successfully")

        except Exception as e:
            logger.error(f"Error generating balance sheet PDF: {e}")
            raise

    def generate_income_statement_pdf(
        self, income_statement_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant income statement PDF."""
        try:
            logger.info(f"Generating income statement PDF: {output_path}")

            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )

            story = []

            # Header information
            header = income_statement_data.get("header", {})
            story.append(
                Paragraph(
                    header.get("company_name", "Company Name"),
                    self.styles["CompanyHeader"],
                )
            )
            story.append(
                Paragraph(
                    header.get("statement_title", "INCOME STATEMENT"),
                    self.styles["StatementTitle"],
                )
            )
            story.append(
                Paragraph(
                    f"For the {header.get('period_description', 'Period')}",
                    self.styles["Normal"],
                )
            )

            if header.get("comparative_period"):
                story.append(
                    Paragraph(
                        f"(With comparative figures for the {header.get('comparative_period')})",
                        self.styles["Normal"],
                    )
                )

            story.append(
                Paragraph(
                    f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')}, except per share data)",
                    self.styles["Normal"],
                )
            )
            story.append(Spacer(1, 20))

            # Revenue section
            story.append(Paragraph("REVENUE", self.styles["SectionHeader"]))
            revenue_table_data = self._build_income_statement_revenue_table(
                income_statement_data.get("revenue", {})
            )
            revenue_table = self._create_financial_table(revenue_table_data)
            story.append(revenue_table)
            story.append(Spacer(1, 12))

            # Cost of revenue and gross profit
            cost_table_data = self._build_income_statement_cost_table(
                income_statement_data.get("cost_of_revenue", {}),
                income_statement_data.get("gross_profit", {}),
            )
            cost_table = self._create_financial_table(cost_table_data)
            story.append(cost_table)
            story.append(Spacer(1, 12))

            # Operating expenses and operating income
            opex_table_data = self._build_income_statement_opex_table(
                income_statement_data.get("operating_expenses", {}),
                income_statement_data.get("operating_income", {}),
            )
            opex_table = self._create_financial_table(opex_table_data)
            story.append(opex_table)
            story.append(Spacer(1, 12))

            # Non-operating and net income
            final_table_data = self._build_income_statement_final_table(
                income_statement_data.get("non_operating_income_expense", {}),
                income_statement_data.get("income_before_taxes", {}),
                income_statement_data.get("income_tax_expense", {}),
                income_statement_data.get("net_income", {}),
            )
            final_table = self._create_financial_table(final_table_data)
            story.append(final_table)
            story.append(Spacer(1, 20))

            # Earnings per share
            if "earnings_per_share" in income_statement_data:
                eps_table_data = self._build_eps_table(
                    income_statement_data["earnings_per_share"]
                )
                eps_table = self._create_financial_table(eps_table_data)
                story.append(eps_table)
                story.append(Spacer(1, 20))

            # Financial analysis
            if "financial_analysis" in income_statement_data:
                story.append(PageBreak())
                story.append(
                    Paragraph("FINANCIAL ANALYSIS", self.styles["SectionHeader"])
                )
                analysis_table_data = self._build_income_analysis_table(
                    income_statement_data["financial_analysis"]
                )
                analysis_table = self._create_financial_table(analysis_table_data)
                story.append(analysis_table)

            doc.build(story)
            logger.info("Income statement PDF generated successfully")

        except Exception as e:
            logger.error(f"Error generating income statement PDF: {e}")
            raise

    def generate_balance_sheet_excel(
        self, balance_sheet_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant balance sheet Excel file."""
        try:
            logger.info(f"Generating balance sheet Excel: {output_path}")

            wb = Workbook()
            ws = wb.active
            ws.title = "Balance Sheet"

            # Setup styles
            header_font = Font(bold=True, size=14)
            section_font = Font(bold=True, size=12)
            currency_format = "#,##0"

            row = 1

            # Header information
            header = balance_sheet_data.get("header", {})
            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("company_name", "Company Name")
            ws[f"A{row}"].font = header_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("statement_title", "BALANCE SHEET")
            ws[f"A{row}"].font = section_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = f"As of {header.get('reporting_date', 'Date')}"
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            if header.get("comparative_date"):
                ws.merge_cells(f"A{row}:D{row}")
                ws[f"A{row}"] = (
                    f"(With comparative figures as of {header.get('comparative_date')})"
                )
                ws[f"A{row}"].alignment = Alignment(horizontal="center")
                row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = (
                f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')})"
            )
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 2

            # Column headers
            ws["A" + str(row)] = "Account"
            ws["B" + str(row)] = header.get("reporting_date", "Current")
            if header.get("comparative_date"):
                ws["C" + str(row)] = header.get("comparative_date", "Comparative")

            for col in ["A", "B", "C"]:
                ws[col + str(row)].font = section_font
            row += 1

            # Assets section
            row = self._add_excel_balance_sheet_section(
                ws, row, "ASSETS", balance_sheet_data.get("assets", {}), currency_format
            )
            row += 1

            # Liabilities and Equity section
            row = self._add_excel_balance_sheet_section(
                ws,
                row,
                "LIABILITIES AND STOCKHOLDERS' EQUITY",
                balance_sheet_data.get("liabilities_and_equity", {}),
                currency_format,
            )

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except (TypeError, ValueError):
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Add financial analysis sheet
            if "financial_analysis" in balance_sheet_data:
                analysis_ws = wb.create_sheet("Financial Analysis")
                self._add_excel_financial_analysis(
                    analysis_ws, balance_sheet_data["financial_analysis"]
                )

            wb.save(output_path)
            logger.info("Balance sheet Excel generated successfully")

        except Exception as e:
            logger.error(f"Error generating balance sheet Excel: {e}")
            raise

    def generate_income_statement_excel(
        self, income_statement_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant income statement Excel file."""
        try:
            logger.info(f"Generating income statement Excel: {output_path}")

            wb = Workbook()
            ws = wb.active
            ws.title = "Income Statement"

            # Setup styles
            header_font = Font(bold=True, size=14)
            section_font = Font(bold=True, size=12)
            currency_format = "#,##0"

            row = 1

            # Header information
            header = income_statement_data.get("header", {})
            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("company_name", "Company Name")
            ws[f"A{row}"].font = header_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("statement_title", "INCOME STATEMENT")
            ws[f"A{row}"].font = section_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = f"For the {header.get('period_description', 'Period')}"
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            if header.get("comparative_period"):
                ws.merge_cells(f"A{row}:D{row}")
                ws[f"A{row}"] = (
                    f"(With comparative figures for the {header.get('comparative_period')})"
                )
                ws[f"A{row}"].alignment = Alignment(horizontal="center")
                row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = (
                f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')}, except per share data)"
            )
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 2

            # Column headers
            ws["A" + str(row)] = "Account"
            ws["B" + str(row)] = "Current Period"
            if header.get("comparative_period"):
                ws["C" + str(row)] = "Comparative Period"

            for col in ["A", "B", "C"]:
                ws[col + str(row)].font = section_font
            row += 1

            # Add income statement sections
            sections = [
                ("REVENUE", income_statement_data.get("revenue", {})),
                ("COST OF REVENUE", income_statement_data.get("cost_of_revenue", {})),
                (
                    "GROSS PROFIT",
                    {"gross_profit": income_statement_data.get("gross_profit", {})},
                ),
                (
                    "OPERATING EXPENSES",
                    income_statement_data.get("operating_expenses", {}),
                ),
                (
                    "OPERATING INCOME",
                    {
                        "operating_income": income_statement_data.get(
                            "operating_income", {}
                        )
                    },
                ),
                (
                    "NON-OPERATING INCOME (EXPENSE)",
                    income_statement_data.get("non_operating_income_expense", {}),
                ),
                (
                    "INCOME BEFORE TAXES",
                    {
                        "income_before_taxes": income_statement_data.get(
                            "income_before_taxes", {}
                        )
                    },
                ),
                (
                    "INCOME TAX EXPENSE",
                    {
                        "income_tax_expense": income_statement_data.get(
                            "income_tax_expense", {}
                        )
                    },
                ),
                (
                    "NET INCOME",
                    {"net_income": income_statement_data.get("net_income", {})},
                ),
            ]

            for section_name, section_data in sections:
                row = self._add_excel_income_statement_section(
                    ws, row, section_name, section_data, currency_format
                )
                row += 1

            # Add EPS section
            if "earnings_per_share" in income_statement_data:
                row = self._add_excel_eps_section(
                    ws, row, income_statement_data["earnings_per_share"]
                )

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except (TypeError, ValueError):
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Add financial analysis sheet
            if "financial_analysis" in income_statement_data:
                analysis_ws = wb.create_sheet("Financial Analysis")
                self._add_excel_income_analysis(
                    analysis_ws, income_statement_data["financial_analysis"]
                )

            wb.save(output_path)
            logger.info("Income statement Excel generated successfully")

        except Exception as e:
            logger.error(f"Error generating income statement Excel: {e}")
            raise

    # Helper methods for PDF table building
    def _build_balance_sheet_assets_table(
        self, assets_data: Dict[str, Any]
    ) -> List[List[str]]:
        """Build assets table data for PDF."""
        table_data = []

        # Current assets
        current_assets = assets_data.get("current_assets", {})
        table_data.append(["CURRENT ASSETS:", "", ""])

        for key, value in current_assets.items():
            if key != "title" and key != "total_current_assets":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total current assets
        total_current = current_assets.get("total_current_assets", {})
        table_data.append(
            [
                "  Total current assets",
                self._format_currency(total_current.get("current", 0)),
                (
                    self._format_currency(total_current.get("comparative", 0))
                    if total_current.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Non-current assets
        non_current_assets = assets_data.get("non_current_assets", {})
        table_data.append(["NON-CURRENT ASSETS:", "", ""])

        for key, value in non_current_assets.items():
            if key != "title" and key != "total_non_current_assets":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total non-current assets
        total_non_current = non_current_assets.get("total_non_current_assets", {})
        table_data.append(
            [
                "  Total non-current assets",
                self._format_currency(total_non_current.get("current", 0)),
                (
                    self._format_currency(total_non_current.get("comparative", 0))
                    if total_non_current.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Total assets
        total_assets = assets_data.get("total_assets", {})
        table_data.append(
            [
                "TOTAL ASSETS",
                self._format_currency(total_assets.get("current", 0)),
                (
                    self._format_currency(total_assets.get("comparative", 0))
                    if total_assets.get("comparative") is not None
                    else ""
                ),
            ]
        )

        return table_data

    def _build_balance_sheet_liabilities_table(
        self, liab_equity_data: Dict[str, Any]
    ) -> List[List[str]]:
        """Build liabilities and equity table data for PDF."""
        table_data = []

        # Current liabilities
        current_liabilities = liab_equity_data.get("current_liabilities", {})
        table_data.append(["CURRENT LIABILITIES:", "", ""])

        for key, value in current_liabilities.items():
            if key != "title" and key != "total_current_liabilities":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total current liabilities
        total_current_liab = current_liabilities.get("total_current_liabilities", {})
        table_data.append(
            [
                "  Total current liabilities",
                self._format_currency(total_current_liab.get("current", 0)),
                (
                    self._format_currency(total_current_liab.get("comparative", 0))
                    if total_current_liab.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Non-current liabilities
        non_current_liabilities = liab_equity_data.get("non_current_liabilities", {})
        table_data.append(["NON-CURRENT LIABILITIES:", "", ""])

        for key, value in non_current_liabilities.items():
            if key != "title" and key != "total_non_current_liabilities":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total non-current liabilities
        total_non_current_liab = non_current_liabilities.get(
            "total_non_current_liabilities", {}
        )
        table_data.append(
            [
                "  Total non-current liabilities",
                self._format_currency(total_non_current_liab.get("current", 0)),
                (
                    self._format_currency(total_non_current_liab.get("comparative", 0))
                    if total_non_current_liab.get("comparative") is not None
                    else ""
                ),
            ]
        )

        # Total liabilities
        total_liabilities = liab_equity_data.get("total_liabilities", {})
        table_data.append(
            [
                "  Total liabilities",
                self._format_currency(total_liabilities.get("current", 0)),
                (
                    self._format_currency(total_liabilities.get("comparative", 0))
                    if total_liabilities.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Stockholders' equity
        stockholders_equity = liab_equity_data.get("stockholders_equity", {})
        table_data.append(["STOCKHOLDERS' EQUITY:", "", ""])

        for key, value in stockholders_equity.items():
            if key != "title" and key != "total_stockholders_equity":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total stockholders' equity
        total_equity = stockholders_equity.get("total_stockholders_equity", {})
        table_data.append(
            [
                "  Total stockholders' equity",
                self._format_currency(total_equity.get("current", 0)),
                (
                    self._format_currency(total_equity.get("comparative", 0))
                    if total_equity.get("comparative") is not None
                    else ""
                ),
            ]
        )

        # Total liabilities and equity
        total_liab_equity = liab_equity_data.get("total_liabilities_and_equity", {})
        table_data.append(
            [
                "TOTAL LIABILITIES AND EQUITY",
                self._format_currency(total_liab_equity.get("current", 0)),
                (
                    self._format_currency(total_liab_equity.get("comparative", 0))
                    if total_liab_equity.get("comparative") is not None
                    else ""
                ),
            ]
        )

        return table_data

    def _create_financial_table(self, table_data: List[List[str]]) -> Table:
        """Create a formatted financial table for PDF."""
        table = Table(table_data, colWidths=[4 * inch, 1.5 * inch, 1.5 * inch])

        table.setStyle(
            TableStyle(
                [
                    ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                    ("ALIGN", (1, 0), (-1, -1), "RIGHT"),
                    ("FONTNAME", (0, 0), (-1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 0), (-1, -1), 10),
                    ("GRID", (0, 0), (-1, -1), 0.5, colors.black),
                    ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                ]
            )
        )

        return table

    def _format_currency(self, amount: float) -> str:
        """Format currency amount for display."""
        if amount == 0:
            return "-"
        return f"{amount:,}"

    def _build_income_statement_revenue_table(self, revenue_data: Dict[str, Any]) -> List[List[str]]:
        """Build revenue table data for income statement PDF."""
        table_data = []
        for key, value in revenue_data.items():
            if key != "title":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])
        return table_data

    def _build_income_statement_cost_table(self, cost_data: Dict[str, Any], gross_profit_data: Dict[str, Any]) -> List[List[str]]:
        """Build cost and gross profit table data."""
        table_data = []
        
        # Cost of revenue items
        for key, value in cost_data.items():
            if key != "title":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])
        
        # Gross profit
        table_data.append(["", "", ""])  # Spacer
        table_data.append([
            "GROSS PROFIT",
            self._format_currency(gross_profit_data.get("current", 0)),
            (
                self._format_currency(gross_profit_data.get("comparative", 0))
                if gross_profit_data.get("comparative") is not None
                else ""
            )
        ])
        
        return table_data

    def _build_income_statement_opex_table(self, opex_data: Dict[str, Any], operating_income_data: Dict[str, Any]) -> List[List[str]]:
        """Build operating expenses and operating income table."""
        table_data = []
        
        # Operating expenses
        table_data.append(["OPERATING EXPENSES:", "", ""])
        for key, value in opex_data.items():
            if key != "title":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])
        
        # Operating income
        table_data.append(["", "", ""])  # Spacer
        table_data.append([
            "OPERATING INCOME",
            self._format_currency(operating_income_data.get("current", 0)),
            (
                self._format_currency(operating_income_data.get("comparative", 0))
                if operating_income_data.get("comparative") is not None
                else ""
            )
        ])
        
        return table_data

    def _build_income_statement_final_table(self, non_op_data: Dict[str, Any], 
                                          income_before_tax_data: Dict[str, Any],
                                          tax_data: Dict[str, Any], 
                                          net_income_data: Dict[str, Any]) -> List[List[str]]:
        """Build final sections of income statement."""
        table_data = []
        
        # Non-operating income/expense
        if non_op_data:
            table_data.append(["NON-OPERATING INCOME (EXPENSE):", "", ""])
            for key, value in non_op_data.items():
                if key != "title":
                    label = key.replace("_", " ").title()
                    current_val = self._format_currency(value.get("current", 0))
                    comp_val = (
                        self._format_currency(value.get("comparative", 0))
                        if value.get("comparative") is not None
                        else ""
                    )
                    table_data.append([f"  {label}", current_val, comp_val])
            
            table_data.append(["", "", ""])  # Spacer
        
        # Income before taxes
        table_data.append([
            "INCOME BEFORE TAXES",
            self._format_currency(income_before_tax_data.get("current", 0)),
            (
                self._format_currency(income_before_tax_data.get("comparative", 0))
                if income_before_tax_data.get("comparative") is not None
                else ""
            )
        ])
        
        # Tax expense
        table_data.append([
            "Income tax expense",
            self._format_currency(tax_data.get("current", 0)),
            (
                self._format_currency(tax_data.get("comparative", 0))
                if tax_data.get("comparative") is not None
                else ""
            )
        ])
        
        # Net income
        table_data.append(["", "", ""])  # Spacer
        table_data.append([
            "NET INCOME",
            self._format_currency(net_income_data.get("current", 0)),
            (
                self._format_currency(net_income_data.get("comparative", 0))
                if net_income_data.get("comparative") is not None
                else ""
            )
        ])
        
        return table_data

    def _build_eps_table(self, eps_data: Dict[str, Any]) -> List[List[str]]:
        """Build earnings per share table."""
        table_data = []
        table_data.append(["EARNINGS PER SHARE:", "", ""])
        
        for key, value in eps_data.items():
            label = key.replace("_", " ").title()
            current_val = f"${value.get('current', 0):.2f}"
            comp_val = f"${value.get('comparative', 0):.2f}" if value.get('comparative') is not None else ""
            table_data.append([f"  {label}", current_val, comp_val])
        
        return table_data

    def _build_financial_analysis_table(self, analysis_data: Dict[str, Any]) -> List[List[str]]:
        """Build financial analysis table for PDFs."""
        table_data = []
        
        if "ratios" in analysis_data:
            table_data.append(["FINANCIAL RATIOS:", "", ""])
            ratios = analysis_data["ratios"]
            
            for category, ratio_data in ratios.items():
                category_label = category.replace("_", " ").title()
                table_data.append([f"{category_label}:", "", ""])
                
                for ratio_name, ratio_value in ratio_data.items():
                    ratio_label = ratio_name.replace("_", " ").title()
                    if isinstance(ratio_value, dict):
                        current_val = f"{ratio_value.get('current', 0):.2f}"
                        comp_val = f"{ratio_value.get('comparative', 0):.2f}" if ratio_value.get('comparative') is not None else ""
                        table_data.append([f"  {ratio_label}", current_val, comp_val])
                    else:
                        table_data.append([f"  {ratio_label}", f"{ratio_value:.2f}", ""])
                
                table_data.append(["", "", ""])  # Spacer
        
        return table_data

    def _build_income_analysis_table(self, analysis_data: Dict[str, Any]) -> List[List[str]]:
        """Build income statement analysis table."""
        table_data = []
        
        # Profitability metrics
        if "profitability_metrics" in analysis_data:
            table_data.append(["PROFITABILITY METRICS:", "", ""])
            metrics = analysis_data["profitability_metrics"]
            
            for metric_name, metric_value in metrics.items():
                metric_label = metric_name.replace("_", " ").title()
                if isinstance(metric_value, dict):
                    current_val = f"{metric_value.get('current', 0)*100:.1f}%"
                    comp_val = f"{metric_value.get('comparative', 0)*100:.1f}%" if metric_value.get('comparative') is not None else ""
                    table_data.append([f"  {metric_label}", current_val, comp_val])
                else:
                    table_data.append([f"  {metric_label}", f"{metric_value*100:.1f}%", ""])
        
        return table_data

    # Excel helper methods
    def _add_excel_balance_sheet_section(self, ws, start_row: int, section_name: str, 
                                       section_data: Dict[str, Any], currency_format: str) -> int:
        """Add a balance sheet section to Excel worksheet."""
        row = start_row
        
        # Section header
        ws[f"A{row}"] = section_name
        ws[f"A{row}"].font = Font(bold=True)
        row += 1
        
        # Add items
        for key, value in section_data.items():
            if key not in ["title", f"total_{section_name.lower().replace(' ', '_')}"]:
                label = key.replace("_", " ").title()
                ws[f"A{row}"] = f"  {label}"
                ws[f"B{row}"] = value.get("current", 0)
                ws[f"B{row}"].number_format = currency_format
                
                if value.get("comparative") is not None:
                    ws[f"C{row}"] = value.get("comparative", 0)
                    ws[f"C{row}"].number_format = currency_format
                
                row += 1
        
        # Add total if present
        total_key = f"total_{section_name.lower().replace(' ', '_').replace('and_', '').replace('stockholders_', '')}"
        if total_key in section_data or "total_assets" in section_data or "total_liabilities_and_equity" in section_data:
            total_data = section_data.get(total_key) or section_data.get("total_assets") or section_data.get("total_liabilities_and_equity")
            if total_data:
                ws[f"A{row}"] = f"  Total {section_name.lower()}"
                ws[f"A{row}"].font = Font(bold=True)
                ws[f"B{row}"] = total_data.get("current", 0)
                ws[f"B{row}"].number_format = currency_format
                ws[f"B{row}"].font = Font(bold=True)
                
                if total_data.get("comparative") is not None:
                    ws[f"C{row}"] = total_data.get("comparative", 0)
                    ws[f"C{row}"].number_format = currency_format
                    ws[f"C{row}"].font = Font(bold=True)
                
                row += 1
        
        return row + 1  # Add extra spacing

    def _add_excel_income_statement_section(self, ws, start_row: int, section_name: str, 
                                          section_data: Dict[str, Any], currency_format: str) -> int:
        """Add income statement section to Excel worksheet."""
        row = start_row
        
        # Section header
        ws[f"A{row}"] = section_name
        ws[f"A{row}"].font = Font(bold=True)
        row += 1
        
        # Add items
        for key, value in section_data.items():
            label = key.replace("_", " ").title()
            ws[f"A{row}"] = f"  {label}"
            
            if isinstance(value, dict):
                ws[f"B{row}"] = value.get("current", 0)
                ws[f"B{row}"].number_format = currency_format
                
                if value.get("comparative") is not None:
                    ws[f"C{row}"] = value.get("comparative", 0)
                    ws[f"C{row}"].number_format = currency_format
            else:
                ws[f"B{row}"] = value
                ws[f"B{row}"].number_format = currency_format
            
            row += 1
        
        return row + 1  # Add extra spacing

    def _add_excel_eps_section(self, ws, start_row: int, eps_data: Dict[str, Any]) -> int:
        """Add earnings per share section to Excel."""
        row = start_row
        
        ws[f"A{row}"] = "EARNINGS PER SHARE"
        ws[f"A{row}"].font = Font(bold=True)
        row += 1
        
        for key, value in eps_data.items():
            label = key.replace("_", " ").title()
            ws[f"A{row}"] = f"  {label}"
            ws[f"B{row}"] = value.get("current", 0)
            ws[f"B{row}"].number_format = "$0.00"
            
            if value.get("comparative") is not None:
                ws[f"C{row}"] = value.get("comparative", 0)
                ws[f"C{row}"].number_format = "$0.00"
            
            row += 1
        
        return row + 1

    def _add_excel_financial_analysis(self, ws, analysis_data: Dict[str, Any]):
        """Add financial analysis to Excel worksheet."""
        row = 1
        
        # Header
        ws.merge_cells(f"A{row}:C{row}")
        ws[f"A{row}"] = "FINANCIAL ANALYSIS"
        ws[f"A{row}"].font = Font(bold=True, size=14)
        ws[f"A{row}"].alignment = Alignment(horizontal="center")
        row += 2
        
        # Column headers
        ws["A" + str(row)] = "Metric"
        ws["B" + str(row)] = "Current"
        ws["C" + str(row)] = "Comparative"
        
        for col in ["A", "B", "C"]:
            ws[col + str(row)].font = Font(bold=True)
        row += 1
        
        # Add analysis data
        if "ratios" in analysis_data:
            for category, ratios in analysis_data["ratios"].items():
                category_label = category.replace("_", " ").title()
                ws[f"A{row}"] = f"{category_label}:"
                ws[f"A{row}"].font = Font(bold=True)
                row += 1
                
                for ratio_name, ratio_value in ratios.items():
                    ratio_label = ratio_name.replace("_", " ").title()
                    ws[f"A{row}"] = f"  {ratio_label}"
                    
                    if isinstance(ratio_value, dict):
                        ws[f"B{row}"] = ratio_value.get("current", 0)
                        if ratio_value.get("comparative") is not None:
                            ws[f"C{row}"] = ratio_value.get("comparative", 0)
                    else:
                        ws[f"B{row}"] = ratio_value
                    
                    row += 1
                
                row += 1  # Extra spacing between categories

    def _add_excel_income_analysis(self, ws, analysis_data: Dict[str, Any]):
        """Add income analysis to Excel worksheet."""
        row = 1
        
        # Header
        ws.merge_cells(f"A{row}:C{row}")
        ws[f"A{row}"] = "INCOME STATEMENT ANALYSIS"
        ws[f"A{row}"].font = Font(bold=True, size=14)
        ws[f"A{row}"].alignment = Alignment(horizontal="center")
        row += 2
        
        # Column headers
        ws["A" + str(row)] = "Metric"
        ws["B" + str(row)] = "Current (%)"
        ws["C" + str(row)] = "Comparative (%)"
        
        for col in ["A", "B", "C"]:
            ws[col + str(row)].font = Font(bold=True)
        row += 1
        
        # Add profitability metrics
        if "profitability_metrics" in analysis_data:
            for metric_name, metric_value in analysis_data["profitability_metrics"].items():
                metric_label = metric_name.replace("_", " ").title()
                ws[f"A{row}"] = metric_label
                
                if isinstance(metric_value, dict):
                    ws[f"B{row}"] = metric_value.get("current", 0) * 100
                    ws[f"B{row}"].number_format = "0.0%"
                    if metric_value.get("comparative") is not None:
                        ws[f"C{row}"] = metric_value.get("comparative", 0) * 100
                        ws[f"C{row}"].number_format = "0.0%"
                else:
                    ws[f"B{row}"] = metric_value * 100
                    ws[f"B{row}"].number_format = "0.0%"
                
                row += 1
    
    # DCF Valuation Report Templates
    def generate_dcf_valuation_pdf(self, dcf_data: Dict[str, Any], output_path: str):
        """Generate professional DCF valuation PDF report."""
        try:
            logger.info(f"Generating DCF valuation PDF: {output_path}")
            
            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )
            
            story = []
            
            # Executive Summary Header
            story.append(Paragraph("DCF VALUATION REPORT", self.styles["CompanyHeader"]))
            story.append(Paragraph(f"Valuation Date: {dcf_data.get('valuation_date', 'N/A')}", self.styles["Normal"]))
            story.append(Spacer(1, 20))
            
            # Executive Summary
            if "executive_summary" in dcf_data:
                summary = dcf_data["executive_summary"]
                story.append(Paragraph("EXECUTIVE SUMMARY", self.styles["SectionHeader"]))
                
                summary_data = [
                    ["Company Name:", summary.get("company_name", "N/A")],
                    ["Valuation Method:", "Discounted Cash Flow (DCF)"],
                    ["Enterprise Value:", f"${summary.get('base_enterprise_value', 0):,.2f}"],
                    ["Equity Value:", f"${summary.get('equity_value', 0):,.2f}"],
                    ["Discount Rate:", f"{summary.get('discount_rate', 0)*100:.1f}%"],
                    ["Terminal Growth:", f"{summary.get('terminal_growth_rate', 0)*100:.1f}%"],
                    ["Projection Period:", f"{summary.get('projection_years', 5)} years"],
                ]
                
                if summary.get('value_per_share'):
                    summary_data.append(["Value per Share:", f"${summary.get('value_per_share', 0):.2f}"])
                
                summary_table = self._create_financial_table(summary_data)
                story.append(summary_table)
                story.append(Spacer(1, 20))
            
            # Key Assumptions
            if "key_assumptions" in dcf_data:
                story.append(Paragraph("KEY ASSUMPTIONS", self.styles["SectionHeader"]))
                assumptions = dcf_data["key_assumptions"]
                
                assumptions_data = []
                for key, value in assumptions.items():
                    key_label = key.replace("_", " ").title()
                    if isinstance(value, (int, float)):
                        if "rate" in key.lower() or "growth" in key.lower():
                            assumptions_data.append([key_label, f"{value*100:.1f}%"])
                        else:
                            assumptions_data.append([key_label, f"${value:,.2f}"])
                    else:
                        assumptions_data.append([key_label, str(value)])
                
                assumptions_table = self._create_financial_table(assumptions_data)
                story.append(assumptions_table)
                story.append(Spacer(1, 20))
            
            # Financial Projections
            if "financial_projections" in dcf_data:
                story.append(PageBreak())
                story.append(Paragraph("FINANCIAL PROJECTIONS", self.styles["SectionHeader"]))
                
                projections = dcf_data["financial_projections"]
                if projections:
                    # Create projection table
                    projection_data = [["Metric"] + [f"Year {i}" for i in range(1, len(projections)+1)]]
                    
                    # Revenue row
                    revenue_row = ["Revenue"] + [f"${proj.get('revenue', 0):,.0f}" for proj in projections]
                    projection_data.append(revenue_row)
                    
                    # Free Cash Flow row
                    fcf_row = ["Free Cash Flow"] + [f"${proj.get('free_cash_flow', 0):,.0f}" for proj in projections]
                    projection_data.append(fcf_row)
                    
                    # Growth rates
                    growth_row = ["Revenue Growth"] + [f"{proj.get('revenue_growth_rate', 0)*100:.1f}%" for proj in projections]
                    projection_data.append(growth_row)
                    
                    projection_table = Table(projection_data, colWidths=[2*inch] + [1*inch]*(len(projections)))
                    projection_table.setStyle(TableStyle([
                        ("ALIGN", (0, 0), (-1, -1), "CENTER"),
                        ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                        ("FONTNAME", (0, 1), (0, -1), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (-1, -1), 10),
                        ("GRID", (0, 0), (-1, -1), 0.5, colors.black),
                        ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                    ]))
                    
                    story.append(projection_table)
                    story.append(Spacer(1, 20))
            
            # Sensitivity Analysis
            if "sensitivity_analysis" in dcf_data:
                story.append(Paragraph("SENSITIVITY ANALYSIS", self.styles["SectionHeader"]))
                sensitivity = dcf_data["sensitivity_analysis"]
                
                sensitivity_data = [
                    ["Valuation Range:", f"${sensitivity.get('min_valuation', 0):,.2f} - ${sensitivity.get('max_valuation', 0):,.2f}"],
                    ["Volatility:", f"{sensitivity.get('volatility', 0)*100:.1f}%"],
                    ["Base Case:", f"${sensitivity.get('base_case_valuation', 0):,.2f}"],
                    ["Upside Case:", f"${sensitivity.get('upside_valuation', 0):,.2f}"],
                    ["Downside Case:", f"${sensitivity.get('downside_valuation', 0):,.2f}"],
                ]
                
                sensitivity_table = self._create_financial_table(sensitivity_data)
                story.append(sensitivity_table)
                story.append(Spacer(1, 20))
            
            # Monte Carlo Results
            if "monte_carlo_simulation" in dcf_data:
                mc_data = dcf_data["monte_carlo_simulation"]
                if "error" not in mc_data:
                    story.append(Paragraph("MONTE CARLO SIMULATION", self.styles["SectionHeader"]))
                    
                    mc_results = [
                        ["Number of Simulations:", f"{mc_data.get('num_simulations', 0):,}"],
                        ["Mean Valuation:", f"${mc_data.get('mean_valuation', 0):,.2f}"],
                        ["Standard Deviation:", f"${mc_data.get('std_deviation', 0):,.2f}"],
                        ["5th Percentile:", f"${mc_data.get('percentiles', {}).get('p5', 0):,.2f}"],
                        ["95th Percentile:", f"${mc_data.get('percentiles', {}).get('p95', 0):,.2f}"],
                        ["Probability > $0:", f"{mc_data.get('probability_positive', 0)*100:.1f}%"],
                    ]
                    
                    mc_table = self._create_financial_table(mc_results)
                    story.append(mc_table)
                    story.append(Spacer(1, 20))
            
            # Methodology notes
            story.append(PageBreak())
            story.append(Paragraph("METHODOLOGY NOTES", self.styles["SectionHeader"]))
            methodology_text = """
            The Discounted Cash Flow (DCF) method estimates the intrinsic value of a business by projecting 
            future free cash flows and discounting them to present value using an appropriate discount rate. 
            This analysis includes:
            
            • Detailed financial projections based on historical performance and growth assumptions
            • Terminal value calculation using the Gordon Growth Model
            • Sensitivity analysis to assess valuation range under different scenarios
            • Monte Carlo simulation (if applicable) to model uncertainty and risk
            
            Key assumptions should be reviewed and validated based on company-specific factors and 
            industry benchmarks.
            """
            story.append(Paragraph(methodology_text, self.styles["Normal"]))
            
            doc.build(story)
            logger.info("DCF valuation PDF generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating DCF valuation PDF: {e}")
            raise
    
    def generate_dcf_valuation_excel(self, dcf_data: Dict[str, Any], output_path: str):
        """Generate detailed DCF valuation Excel model."""
        try:
            logger.info(f"Generating DCF valuation Excel: {output_path}")
            
            wb = Workbook()
            
            # Remove default sheet and create custom sheets
            wb.remove(wb.active)
            
            # Executive Summary Sheet
            summary_ws = wb.create_sheet("Executive Summary")
            self._add_dcf_executive_summary_sheet(summary_ws, dcf_data)
            
            # Financial Projections Sheet
            if "financial_projections" in dcf_data:
                proj_ws = wb.create_sheet("Financial Projections")
                self._add_dcf_projections_sheet(proj_ws, dcf_data["financial_projections"])
            
            # DCF Calculation Sheet
            dcf_ws = wb.create_sheet("DCF Analysis")
            self._add_dcf_calculation_sheet(dcf_ws, dcf_data)
            
            # Sensitivity Analysis Sheet
            if "sensitivity_analysis" in dcf_data:
                sens_ws = wb.create_sheet("Sensitivity Analysis")
                self._add_dcf_sensitivity_sheet(sens_ws, dcf_data["sensitivity_analysis"])
            
            # Assumptions Sheet
            if "key_assumptions" in dcf_data:
                assumptions_ws = wb.create_sheet("Key Assumptions")
                self._add_dcf_assumptions_sheet(assumptions_ws, dcf_data["key_assumptions"])
            
            wb.save(output_path)
            logger.info("DCF valuation Excel generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating DCF valuation Excel: {e}")
            raise
    
    # DCF Excel helper methods
    def _add_dcf_executive_summary_sheet(self, ws, dcf_data: Dict[str, Any]):
        """Add executive summary to DCF Excel workbook."""
        # Header
        ws.merge_cells("A1:D1")
        ws["A1"] = "DCF VALUATION - EXECUTIVE SUMMARY"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Company Information
        if "executive_summary" in dcf_data:
            summary = dcf_data["executive_summary"]
            
            ws[f"A{row}"] = "Company Information"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            company_data = [
                ["Company Name:", summary.get("company_name", "N/A")],
                ["Valuation Date:", summary.get("valuation_date", "N/A")],
                ["Analysis Performed By:", summary.get("analyst", "MCX3D Finance")],
            ]
            
            for label, value in company_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                row += 1
            
            row += 1
            
            # Valuation Results
            ws[f"A{row}"] = "Valuation Results"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            valuation_data = [
                ["Enterprise Value:", f"${summary.get('base_enterprise_value', 0):,.2f}"],
                ["Equity Value:", f"${summary.get('equity_value', 0):,.2f}"],
                ["Value per Share:", f"${summary.get('value_per_share', 0):.2f}"] if summary.get('value_per_share') else None,
            ]
            
            for item in valuation_data:
                if item:
                    ws[f"A{row}"] = item[0]
                    ws[f"B{row}"] = item[1]
                    ws[f"B{row}"].number_format = "#,##0.00"
                    row += 1
            
            row += 1
            
            # Key Parameters
            ws[f"A{row}"] = "Key Parameters"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            param_data = [
                ["Discount Rate (WACC):", f"{summary.get('discount_rate', 0)*100:.1f}%"],
                ["Terminal Growth Rate:", f"{summary.get('terminal_growth_rate', 0)*100:.1f}%"],
                ["Projection Period:", f"{summary.get('projection_years', 5)} years"],
            ]
            
            for label, value in param_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_dcf_projections_sheet(self, ws, projections: List[Dict[str, Any]]):
        """Add financial projections to DCF Excel workbook."""
        # Header
        ws.merge_cells("A1:F1")
        ws["A1"] = "FINANCIAL PROJECTIONS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        if projections:
            # Column headers
            headers = ["Metric"] + [f"Year {i}" for i in range(1, len(projections)+1)]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.font = Font(bold=True)
            row += 1
            
            # Financial metrics
            metrics = [
                ("Revenue", "revenue"),
                ("Revenue Growth %", "revenue_growth_rate"),
                ("EBITDA", "ebitda"),
                ("EBIT", "ebit"),
                ("NOPAT", "nopat"),
                ("Free Cash Flow", "free_cash_flow"),
                ("FCF Growth %", "fcf_growth_rate"),
            ]
            
            for metric_name, metric_key in metrics:
                ws.cell(row=row, column=1, value=metric_name).font = Font(bold=True)
                
                for col, projection in enumerate(projections, 2):
                    value = projection.get(metric_key, 0)
                    cell = ws.cell(row=row, column=col, value=value)
                    
                    if "%" in metric_name:
                        cell.number_format = "0.0%"
                    else:
                        cell.number_format = "#,##0"
                
                row += 1
            
            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except (TypeError, ValueError):
                        pass
                adjusted_width = min(max_length + 2, 15)
                ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_dcf_calculation_sheet(self, ws, dcf_data: Dict[str, Any]):
        """Add DCF calculation details to Excel workbook."""
        # Header
        ws.merge_cells("A1:D1")
        ws["A1"] = "DCF CALCULATION"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Present Value of Operating FCF
        if "valuation_results" in dcf_data:
            results = dcf_data["valuation_results"]
            
            if isinstance(results, dict):
                # Single scenario
                result_data = list(results.values())[0] if results else {}
            else:
                # Multiple scenarios - use base
                result_data = results.get("base", {})
            
            ws[f"A{row}"] = "DCF Calculation Components"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            calc_data = [
                ["Present Value of Operating FCF:", f"${result_data.get('pv_operating_fcf', 0):,.2f}"],
                ["Terminal Value:", f"${result_data.get('terminal_value', 0):,.2f}"],
                ["PV of Terminal Value:", f"${result_data.get('pv_terminal_value', 0):,.2f}"],
                ["Enterprise Value:", f"${result_data.get('enterprise_value', 0):,.2f}"],
                ["Less: Net Debt:", f"${result_data.get('net_debt', 0):,.2f}"],
                ["Equity Value:", f"${result_data.get('equity_value', 0):,.2f}"],
            ]
            
            for label, value in calc_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                ws[f"A{row}"].font = Font(bold=True)
                row += 1
            
            row += 1
            
            # Key ratios and metrics
            if "financial_metrics" in result_data:
                ws[f"A{row}"] = "Key Financial Metrics"
                ws[f"A{row}"].font = Font(bold=True, size=12)
                row += 1
                
                metrics = result_data["financial_metrics"]
                for metric, value in metrics.items():
                    metric_label = metric.replace("_", " ").title()
                    ws[f"A{row}"] = metric_label
                    if isinstance(value, (int, float)):
                        if "ratio" in metric.lower() or "margin" in metric.lower():
                            ws[f"B{row}"] = f"{value:.2f}"
                        else:
                            ws[f"B{row}"] = f"${value:,.2f}"
                    else:
                        ws[f"B{row}"] = str(value)
                    row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 40)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_dcf_sensitivity_sheet(self, ws, sensitivity_data: Dict[str, Any]):
        """Add sensitivity analysis to Excel workbook."""
        # Header
        ws.merge_cells("A1:E1")
        ws["A1"] = "SENSITIVITY ANALYSIS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Summary statistics
        ws[f"A{row}"] = "Valuation Range Analysis"
        ws[f"A{row}"].font = Font(bold=True, size=12)
        row += 1
        
        range_data = [
            ["Minimum Valuation:", f"${sensitivity_data.get('min_valuation', 0):,.2f}"],
            ["Maximum Valuation:", f"${sensitivity_data.get('max_valuation', 0):,.2f}"],
            ["Base Case:", f"${sensitivity_data.get('base_case_valuation', 0):,.2f}"],
            ["Standard Deviation:", f"${sensitivity_data.get('std_deviation', 0):,.2f}"],
            ["Volatility:", f"{sensitivity_data.get('volatility', 0)*100:.1f}%"],
        ]
        
        for label, value in range_data:
            ws[f"A{row}"] = label
            ws[f"B{row}"] = value
            ws[f"A{row}"].font = Font(bold=True)
            row += 1
        
        row += 2
        
        # Scenario analysis
        if "scenarios" in sensitivity_data:
            ws[f"A{row}"] = "Scenario Analysis"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            scenarios = sensitivity_data["scenarios"]
            for scenario_name, scenario_value in scenarios.items():
                scenario_label = scenario_name.replace("_", " ").title()
                ws[f"A{row}"] = f"{scenario_label} Case:"
                ws[f"B{row}"] = f"${scenario_value:,.2f}"
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_dcf_assumptions_sheet(self, ws, assumptions: Dict[str, Any]):
        """Add key assumptions to Excel workbook."""
        # Header
        ws.merge_cells("A1:C1")
        ws["A1"] = "KEY ASSUMPTIONS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Column headers
        ws[f"A{row}"] = "Assumption"
        ws[f"B{row}"] = "Value"
        ws[f"C{row}"] = "Notes"
        
        for col in ["A", "B", "C"]:
            ws[f"{col}{row}"].font = Font(bold=True)
        row += 1
        
        # Add assumptions
        for key, value in assumptions.items():
            assumption_label = key.replace("_", " ").title()
            ws[f"A{row}"] = assumption_label
            
            if isinstance(value, dict):
                ws[f"B{row}"] = str(value.get("value", "N/A"))
                ws[f"C{row}"] = value.get("notes", "")
            else:
                if isinstance(value, (int, float)):
                    if "rate" in key.lower() or "growth" in key.lower():
                        ws[f"B{row}"] = f"{value*100:.1f}%"
                    else:
                        ws[f"B{row}"] = f"${value:,.2f}"
                else:
                    ws[f"B{row}"] = str(value)
            
            row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    # SaaS Valuation Report Templates
    def generate_saas_valuation_pdf(self, saas_data: Dict[str, Any], output_path: str):
        """Generate professional SaaS valuation PDF report."""
        try:
            logger.info(f"Generating SaaS valuation PDF: {output_path}")
            
            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )
            
            story = []
            
            # Header
            story.append(Paragraph("COMPREHENSIVE SAAS VALUATION REPORT", self.styles["CompanyHeader"]))
            story.append(Paragraph(f"Valuation Date: {saas_data.get('valuation_date', 'N/A')}", self.styles["Normal"]))
            story.append(Spacer(1, 20))
            
            # Executive Summary
            story.append(Paragraph("EXECUTIVE SUMMARY", self.styles["SectionHeader"]))
            
            key_metrics = saas_data.get("key_metrics", {})
            exec_summary_data = [
                ["Organization ID:", str(saas_data.get("organization_id", "N/A"))],
                ["Valuation Method:", saas_data.get("methodology", "Comprehensive SaaS Analysis")],
                ["Annual Recurring Revenue:", f"${key_metrics.get('arr', 0):,.2f}"],
                ["Monthly Recurring Revenue:", f"${key_metrics.get('mrr', 0):,.2f}"],
                ["Growth Rate:", f"{key_metrics.get('growth_rate', 0):.1f}%"],
                ["Customer Churn Rate:", f"{key_metrics.get('churn_rate', 0):.1f}%"],
                ["LTV/CAC Ratio:", f"{key_metrics.get('ltv_cac_ratio', 0):.1f}x"],
                ["Active Customers:", f"{key_metrics.get('active_customers', 0):,}"],
            ]
            
            exec_table = self._create_financial_table(exec_summary_data)
            story.append(exec_table)
            story.append(Spacer(1, 20))
            
            # Weighted Valuation Results
            weighted_val = saas_data.get("weighted_valuation", {})
            if weighted_val and "error" not in weighted_val:
                story.append(Paragraph("VALUATION SUMMARY", self.styles["SectionHeader"]))
                
                val_range = weighted_val.get("valuation_range", {})
                valuation_data = [
                    ["Primary Valuation:", f"${weighted_val.get('weighted_average_valuation', 0):,.2f}"],
                    ["Valuation Range:", f"${val_range.get('minimum', 0):,.2f} - ${val_range.get('maximum', 0):,.2f}"],
                    ["Valuation Spread:", f"{val_range.get('spread_percentage', 0):.1f}%"],
                    ["Methods Used:", str(weighted_val.get("methods_used", 0))],
                ]
                
                val_table = self._create_financial_table(valuation_data)
                story.append(val_table)
                story.append(Spacer(1, 20))
            
            # Individual Valuation Methods
            valuation_methods = saas_data.get("valuation_methods", {})
            if valuation_methods:
                story.append(PageBreak())
                story.append(Paragraph("INDIVIDUAL VALUATION METHODS", self.styles["SectionHeader"]))
                
                for method, data in valuation_methods.items():
                    if "error" not in data:
                        method_name = method.replace("_", " ").title()
                        story.append(Paragraph(f"{method_name} Analysis:", self.styles["Normal"]))
                        
                        method_data = []
                        if method == "arr_multiple":
                            method_data = [
                                ["Base ARR:", f"${data.get('base_arr', 0):,.2f}"],
                                ["Base Multiple:", f"{data.get('base_multiple', 0):.1f}x"],
                                ["Adjusted Multiple:", f"{data.get('adjusted_multiple', 0):.1f}x"],
                                ["Adjusted Valuation:", f"${data.get('adjusted_valuation', 0):,.2f}"],
                                ["Confidence Score:", f"{data.get('confidence_score', 0):.0%}"],
                            ]
                        elif method == "revenue_multiple":
                            method_data = [
                                ["Annual Revenue:", f"${data.get('annual_revenue', 0):,.2f}"],
                                ["Revenue Multiple:", f"{data.get('revenue_multiple', 0):.1f}x"],
                                ["Valuation:", f"${data.get('valuation', 0):,.2f}"],
                            ]
                        elif method == "saas_dcf":
                            method_data = [
                                ["Enterprise Value:", f"${data.get('enterprise_value', 0):,.2f}"],
                                ["PV of Operating FCF:", f"${data.get('pv_operating_fcf', 0):,.2f}"],
                                ["Terminal Value:", f"${data.get('terminal_value', 0):,.2f}"],
                                ["Discount Rate:", f"{data.get('discount_rate', 0)*100:.1f}%"],
                            ]
                        elif method == "unit_economics":
                            method_data = [
                                ["Total Valuation:", f"${data.get('total_valuation', 0):,.2f}"],
                                ["Customer Base Value:", f"${data.get('customer_base_value', 0):,.2f}"],
                                ["LTV/CAC Ratio:", f"{data.get('ltv_cac_ratio', 0):.1f}x"],
                            ]
                        
                        if method_data:
                            method_table = self._create_financial_table(method_data)
                            story.append(method_table)
                            story.append(Spacer(1, 15))
            
            # Business Quality Assessment
            quality_assessment = saas_data.get("quality_assessment", {})
            if quality_assessment and "error" not in quality_assessment:
                story.append(PageBreak())
                story.append(Paragraph("BUSINESS QUALITY ASSESSMENT", self.styles["SectionHeader"]))
                
                quality_data = [
                    ["Overall Score:", f"{quality_assessment.get('total_score', 0)}/100"],
                    ["Quality Grade:", quality_assessment.get("grade", "N/A")],
                    ["Quality Tier:", quality_assessment.get("quality_tier", "N/A")],
                ]
                
                component_scores = quality_assessment.get("component_scores", {})
                if component_scores:
                    for component, score in component_scores.items():
                        component_name = component.replace("_", " ").title()
                        quality_data.append([f"{component_name}:", f"{score}/25"])
                
                quality_table = self._create_financial_table(quality_data)
                story.append(quality_table)
                story.append(Spacer(1, 20))
            
            # Industry Benchmarks
            benchmarks = saas_data.get("industry_benchmarks", {})
            if benchmarks:
                story.append(Paragraph("INDUSTRY BENCHMARKS", self.styles["SectionHeader"]))
                
                key_benchmarks = benchmarks.get("key_metrics", {})
                if key_benchmarks:
                    benchmark_data = []
                    for metric, levels in key_benchmarks.items():
                        metric_name = metric.replace("_", " ").title()
                        if isinstance(levels, dict):
                            excellent = levels.get("excellent", "N/A")
                            good = levels.get("good", "N/A")
                            poor = levels.get("poor", "N/A")
                            if metric == "growth_rate":
                                benchmark_data.append([f"{metric_name} (Excellent):", f"{excellent}%"])
                                benchmark_data.append([f"{metric_name} (Good):", f"{good}%"])
                            elif metric == "churn_rate":
                                benchmark_data.append([f"{metric_name} (Excellent):", f"<{excellent}%"])
                                benchmark_data.append([f"{metric_name} (Poor):", f">{poor}%"])
                            elif metric == "ltv_cac_ratio":
                                benchmark_data.append([f"{metric_name} (Excellent):", f">{excellent}x"])
                                benchmark_data.append([f"{metric_name} (Poor):", f"<{poor}x"])
                    
                    if benchmark_data:
                        benchmark_table = self._create_financial_table(benchmark_data)
                        story.append(benchmark_table)
                        story.append(Spacer(1, 20))
            
            # Methodology Notes
            story.append(PageBreak())
            story.append(Paragraph("METHODOLOGY NOTES", self.styles["SectionHeader"]))
            methodology_text = """
            This comprehensive SaaS valuation employs multiple industry-standard methodologies:
            
            • ARR Multiple Valuation: Values the business based on Annual Recurring Revenue with quality adjustments for growth, unit economics, retention, and scale
            
            • Revenue Multiple Valuation: Traditional revenue-based valuation adjusted for SaaS-specific metrics including growth rates and gross margins
            
            • SaaS-Optimized DCF: Discounted Cash Flow analysis tailored for SaaS businesses with appropriate assumptions for recurring revenue models
            
            • Unit Economics Valuation: Values the business based on customer lifetime value (LTV) and acquisition costs (CAC)
            
            The weighted average combines these methodologies to provide a comprehensive valuation range. Quality assessment scores are based on growth sustainability, unit economics strength, customer retention, and revenue expansion metrics.
            
            All valuations should be considered within the context of current market conditions and company-specific factors.
            """
            story.append(Paragraph(methodology_text, self.styles["Normal"]))
            
            doc.build(story)
            logger.info("SaaS valuation PDF generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating SaaS valuation PDF: {e}")
            raise
    
    def generate_saas_valuation_excel(self, saas_data: Dict[str, Any], output_path: str):
        """Generate detailed SaaS valuation Excel model."""
        try:
            logger.info(f"Generating SaaS valuation Excel: {output_path}")
            
            wb = Workbook()
            wb.remove(wb.active)
            
            # Executive Summary Sheet
            summary_ws = wb.create_sheet("Executive Summary")
            self._add_saas_executive_summary_sheet(summary_ws, saas_data)
            
            # SaaS Metrics Dashboard
            metrics_ws = wb.create_sheet("SaaS Metrics")
            self._add_saas_metrics_sheet(metrics_ws, saas_data)
            
            # Valuation Analysis Sheet
            valuation_ws = wb.create_sheet("Valuation Analysis")
            self._add_saas_valuation_sheet(valuation_ws, saas_data)
            
            # Quality Assessment Sheet
            if "quality_assessment" in saas_data:
                quality_ws = wb.create_sheet("Quality Assessment")
                self._add_saas_quality_sheet(quality_ws, saas_data["quality_assessment"])
            
            # Industry Benchmarks Sheet
            if "industry_benchmarks" in saas_data:
                benchmarks_ws = wb.create_sheet("Industry Benchmarks")
                self._add_saas_benchmarks_sheet(benchmarks_ws, saas_data["industry_benchmarks"])
            
            wb.save(output_path)
            logger.info("SaaS valuation Excel generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating SaaS valuation Excel: {e}")
            raise
    
    # SaaS Excel helper methods
    def _add_saas_executive_summary_sheet(self, ws, saas_data: Dict[str, Any]):
        """Add SaaS executive summary to Excel workbook."""
        # Header
        ws.merge_cells("A1:D1")
        ws["A1"] = "SAAS VALUATION - EXECUTIVE SUMMARY"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Company Information
        ws[f"A{row}"] = "Company Information"
        ws[f"A{row}"].font = Font(bold=True, size=12)
        row += 1
        
        company_data = [
            ["Organization ID:", str(saas_data.get("organization_id", "N/A"))],
            ["Valuation Date:", saas_data.get("valuation_date", "N/A")],
            ["Analysis Period:", f"{saas_data.get('analysis_period', {}).get('start', 'N/A')} to {saas_data.get('analysis_period', {}).get('end', 'N/A')}"],
        ]
        
        for label, value in company_data:
            ws[f"A{row}"] = label
            ws[f"B{row}"] = value
            row += 1
        
        row += 1
        
        # Key SaaS Metrics
        key_metrics = saas_data.get("key_metrics", {})
        if key_metrics:
            ws[f"A{row}"] = "Key SaaS Metrics"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            metrics_data = [
                ["Annual Recurring Revenue:", f"${key_metrics.get('arr', 0):,.2f}"],
                ["Monthly Recurring Revenue:", f"${key_metrics.get('mrr', 0):,.2f}"],
                ["Growth Rate:", f"{key_metrics.get('growth_rate', 0):.1f}%"],
                ["Churn Rate:", f"{key_metrics.get('churn_rate', 0):.1f}%"],
                ["LTV/CAC Ratio:", f"{key_metrics.get('ltv_cac_ratio', 0):.1f}x"],
                ["Net Revenue Retention:", f"{key_metrics.get('nrr', 0):.1f}%"],
                ["Active Customers:", f"{key_metrics.get('active_customers', 0):,}"],
            ]
            
            for label, value in metrics_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                row += 1
        
        row += 1
        
        # Valuation Results
        weighted_val = saas_data.get("weighted_valuation", {})
        if weighted_val and "error" not in weighted_val:
            ws[f"A{row}"] = "Valuation Results"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            val_range = weighted_val.get("valuation_range", {})
            valuation_data = [
                ["Primary Valuation:", f"${weighted_val.get('weighted_average_valuation', 0):,.2f}"],
                ["Minimum Valuation:", f"${val_range.get('minimum', 0):,.2f}"],
                ["Maximum Valuation:", f"${val_range.get('maximum', 0):,.2f}"],
                ["Valuation Spread:", f"{val_range.get('spread_percentage', 0):.1f}%"],
                ["Methods Used:", str(weighted_val.get("methods_used", 0))],
            ]
            
            for label, value in valuation_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                if "$" in value:
                    ws[f"B{row}"].number_format = "#,##0.00"
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 40)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_saas_metrics_sheet(self, ws, saas_data: Dict[str, Any]):
        """Add detailed SaaS metrics to Excel workbook."""
        # Header
        ws.merge_cells("A1:D1")
        ws["A1"] = "SAAS METRICS DASHBOARD"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Extract full KPI analysis
        full_kpis = saas_data.get("full_kpi_analysis", {})
        if full_kpis:
            kpis = full_kpis.get("kpis", {})
            
            # Revenue Metrics Section
            revenue_metrics = kpis.get("revenue_metrics", {})
            if revenue_metrics:
                ws[f"A{row}"] = "Revenue Metrics"
                ws[f"A{row}"].font = Font(bold=True, size=12)
                row += 1
                
                ws[f"A{row}"] = "Metric"
                ws[f"B{row}"] = "Value"
                ws[f"A{row}"].font = Font(bold=True)
                ws[f"B{row}"].font = Font(bold=True)
                row += 1
                
                for metric, value in revenue_metrics.items():
                    metric_name = metric.replace("_", " ").title()
                    ws[f"A{row}"] = metric_name
                    
                    if isinstance(value, dict):
                        # Handle nested metrics like growth rates
                        if "monthly" in value:
                            ws[f"B{row}"] = f"{value.get('monthly', 0):.1f}%"
                        elif "annual" in value:
                            ws[f"B{row}"] = f"{value.get('annual', 0):.1f}%"
                        else:
                            ws[f"B{row}"] = str(value)
                    elif isinstance(value, (int, float)):
                        if "rate" in metric or "percentage" in metric:
                            ws[f"B{row}"] = f"{value:.1f}%"
                        elif "revenue" in metric or "value" in metric:
                            ws[f"B{row}"] = f"${value:,.2f}"
                            ws[f"B{row}"].number_format = "#,##0.00"
                        else:
                            ws[f"B{row}"] = f"{value:,.2f}"
                    else:
                        ws[f"B{row}"] = str(value)
                    
                    row += 1
                
                row += 1
            
            # Customer Metrics Section
            customer_metrics = kpis.get("customer_metrics", {})
            if customer_metrics:
                ws[f"A{row}"] = "Customer Metrics"
                ws[f"A{row}"].font = Font(bold=True, size=12)
                row += 1
                
                ws[f"A{row}"] = "Metric"
                ws[f"B{row}"] = "Value"
                ws[f"A{row}"].font = Font(bold=True)
                ws[f"B{row}"].font = Font(bold=True)
                row += 1
                
                for metric, value in customer_metrics.items():
                    metric_name = metric.replace("_", " ").title()
                    ws[f"A{row}"] = metric_name
                    
                    if isinstance(value, (int, float)):
                        if "rate" in metric or "churn" in metric:
                            ws[f"B{row}"] = f"{value:.1f}%"
                        elif "cost" in metric or "value" in metric:
                            ws[f"B{row}"] = f"${value:,.2f}"
                            ws[f"B{row}"].number_format = "#,##0.00"
                        elif "ratio" in metric:
                            ws[f"B{row}"] = f"{value:.1f}x"
                        elif "customers" in metric:
                            ws[f"B{row}"] = f"{value:,}"
                        else:
                            ws[f"B{row}"] = f"{value:,.2f}"
                    else:
                        ws[f"B{row}"] = str(value)
                    
                    row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 40)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_saas_valuation_sheet(self, ws, saas_data: Dict[str, Any]):
        """Add valuation analysis to Excel workbook."""
        # Header
        ws.merge_cells("A1:E1")
        ws["A1"] = "VALUATION ANALYSIS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Individual Valuation Methods
        valuation_methods = saas_data.get("valuation_methods", {})
        if valuation_methods:
            ws[f"A{row}"] = "Individual Valuation Methods"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            # Headers
            ws[f"A{row}"] = "Method"
            ws[f"B{row}"] = "Valuation"
            ws[f"C{row}"] = "Key Metric"
            ws[f"D{row}"] = "Multiple/Rate"
            ws[f"E{row}"] = "Confidence"
            
            for col in ["A", "B", "C", "D", "E"]:
                ws[f"{col}{row}"].font = Font(bold=True)
            row += 1
            
            for method, data in valuation_methods.items():
                if "error" not in data:
                    method_name = method.replace("_", " ").title()
                    ws[f"A{row}"] = method_name
                    
                    if method == "arr_multiple":
                        ws[f"B{row}"] = data.get("adjusted_valuation", 0)
                        ws[f"C{row}"] = f"${data.get('base_arr', 0):,.0f}"
                        ws[f"D{row}"] = f"{data.get('adjusted_multiple', 0):.1f}x"
                        ws[f"E{row}"] = f"{data.get('confidence_score', 0):.0%}"
                    elif method == "revenue_multiple":
                        ws[f"B{row}"] = data.get("valuation", 0)
                        ws[f"C{row}"] = f"${data.get('annual_revenue', 0):,.0f}"
                        ws[f"D{row}"] = f"{data.get('revenue_multiple', 0):.1f}x"
                        ws[f"E{row}"] = "N/A"
                    elif method == "saas_dcf":
                        ws[f"B{row}"] = data.get("enterprise_value", 0)
                        ws[f"C{row}"] = f"${data.get('pv_operating_fcf', 0):,.0f}"
                        ws[f"D{row}"] = f"{data.get('discount_rate', 0)*100:.1f}%"
                        ws[f"E{row}"] = "High"
                    elif method == "unit_economics":
                        ws[f"B{row}"] = data.get("total_valuation", 0)
                        ws[f"C{row}"] = f"{data.get('active_customers', 0):,}"
                        ws[f"D{row}"] = f"{data.get('ltv_cac_ratio', 0):.1f}x"
                        ws[f"E{row}"] = "Medium"
                    
                    ws[f"B{row}"].number_format = "#,##0"
                    row += 1
            
            row += 2
        
        # Weighted Average Results
        weighted_val = saas_data.get("weighted_valuation", {})
        if weighted_val and "error" not in weighted_val:
            ws[f"A{row}"] = "Weighted Average Valuation"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            val_data = [
                ["Primary Valuation:", weighted_val.get("weighted_average_valuation", 0)],
                ["Minimum:", weighted_val.get("valuation_range", {}).get("minimum", 0)],
                ["Maximum:", weighted_val.get("valuation_range", {}).get("maximum", 0)],
                ["Methods Used:", weighted_val.get("methods_used", 0)],
            ]
            
            for label, value in val_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                if isinstance(value, (int, float)) and value > 1000:
                    ws[f"B{row}"].number_format = "#,##0"
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_saas_quality_sheet(self, ws, quality_data: Dict[str, Any]):
        """Add business quality assessment to Excel workbook."""
        # Header
        ws.merge_cells("A1:C1")
        ws["A1"] = "BUSINESS QUALITY ASSESSMENT"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Overall Quality Score
        ws[f"A{row}"] = "Overall Assessment"
        ws[f"A{row}"].font = Font(bold=True, size=12)
        row += 1
        
        overall_data = [
            ["Total Score:", f"{quality_data.get('total_score', 0)}/100"],
            ["Quality Grade:", quality_data.get("grade", "N/A")],
            ["Quality Tier:", quality_data.get("quality_tier", "N/A")],
        ]
        
        for label, value in overall_data:
            ws[f"A{row}"] = label
            ws[f"B{row}"] = value
            row += 1
        
        row += 1
        
        # Component Scores
        component_scores = quality_data.get("component_scores", {})
        if component_scores:
            ws[f"A{row}"] = "Component Scores"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            ws[f"A{row}"] = "Component"
            ws[f"B{row}"] = "Score"
            ws[f"C{row}"] = "Percentage"
            
            for col in ["A", "B", "C"]:
                ws[f"{col}{row}"].font = Font(bold=True)
            row += 1
            
            for component, score in component_scores.items():
                component_name = component.replace("_", " ").title()
                ws[f"A{row}"] = component_name
                ws[f"B{row}"] = f"{score}/25"
                ws[f"C{row}"] = f"{(score/25)*100:.0f}%"
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_saas_benchmarks_sheet(self, ws, benchmarks_data: Dict[str, Any]):
        """Add industry benchmarks to Excel workbook."""
        # Header
        ws.merge_cells("A1:E1")
        ws["A1"] = "INDUSTRY BENCHMARKS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Key Metrics Benchmarks
        key_metrics = benchmarks_data.get("key_metrics", {})
        if key_metrics:
            ws[f"A{row}"] = "SaaS Industry Benchmarks"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            # Headers
            ws[f"A{row}"] = "Metric"
            ws[f"B{row}"] = "Excellent"
            ws[f"C{row}"] = "Good"
            ws[f"D{row}"] = "Average"
            ws[f"E{row}"] = "Poor"
            
            for col in ["A", "B", "C", "D", "E"]:
                ws[f"{col}{row}"].font = Font(bold=True)
            row += 1
            
            for metric, levels in key_metrics.items():
                if isinstance(levels, dict):
                    metric_name = metric.replace("_", " ").title()
                    ws[f"A{row}"] = metric_name
                    
                    excellent = levels.get("excellent", "N/A")
                    good = levels.get("good", "N/A")
                    average = levels.get("average", "N/A")
                    poor = levels.get("poor", "N/A")
                    
                    if metric == "growth_rate":
                        ws[f"B{row}"] = f">{excellent}%"
                        ws[f"C{row}"] = f">{good}%"
                        ws[f"D{row}"] = f">{average}%"
                        ws[f"E{row}"] = f"<{poor}%"
                    elif metric == "churn_rate":
                        ws[f"B{row}"] = f"<{excellent}%"
                        ws[f"C{row}"] = f"<{good}%"
                        ws[f"D{row}"] = f"<{average}%"
                        ws[f"E{row}"] = f">{poor}%"
                    elif metric == "ltv_cac_ratio":
                        ws[f"B{row}"] = f">{excellent}x"
                        ws[f"C{row}"] = f">{good}x"
                        ws[f"D{row}"] = f">{average}x"
                        ws[f"E{row}"] = f"<{poor}x"
                    elif metric == "net_revenue_retention":
                        ws[f"B{row}"] = f">{excellent}%"
                        ws[f"C{row}"] = f">{good}%"
                        ws[f"D{row}"] = f">{average}%"
                        ws[f"E{row}"] = f"<{poor}%"
                    else:
                        ws[f"B{row}"] = str(excellent)
                        ws[f"C{row}"] = str(good)
                        ws[f"D{row}"] = str(average)
                        ws[f"E{row}"] = str(poor)
                    
                    row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width
