from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from mcx3d_finance.api import auth_routes, auth, webhook_routes, reports, metrics, xero_import_routes
from mcx3d_finance.api.auth_middleware import add_security_headers

app = FastAPI(
    title="MCX3D Financials API",
    description="API for financial reporting and KPI calculation.",
    version="2.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],  # Configure as needed
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add security headers middleware
app.middleware("http")(add_security_headers)

# Include routers
app.include_router(auth.router, tags=["Authentication"])  # New auth endpoints
app.include_router(auth_routes.router, prefix="/auth", tags=["Auth"])  # Legacy auth routes
app.include_router(webhook_routes.router, prefix="/webhooks", tags=["Webhooks"])
app.include_router(reports.router, tags=["Reports"])  # Note: reports router already has /api prefix
app.include_router(metrics.router, prefix="/api", tags=["Metrics"])
app.include_router(xero_import_routes.router, tags=["Xero Import"])


@app.get("/")
def read_root():
    return {"message": "Welcome to the MCX3D Financials API"}
