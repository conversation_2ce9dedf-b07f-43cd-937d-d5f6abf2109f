"""
Xero Data Import Service

Complete data import service that downloads data from Xero and prepares it for database storage.
Uses MCP client with fallback to direct API for maximum reliability.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta, timezone
from decimal import Decimal
import json

from .mcp_xero_client import MCPXeroClient
from .mcp_bridge import MCPXeroBridge
from ..core.data_processors import XeroDataProcessor
from ..core.account_mapper import IndustryType
from ..db.models import Organization, Account, Contact, Transaction
from ..db.session import SessionLocal

logger = logging.getLogger(__name__)


class XeroDataImportService:
    """Service to import all data from Xero with validation and transformation."""
    
    def __init__(self, organization_id: int):
        self.organization_id = organization_id
        self.mcp_client = MCPXeroClient(organization_id)
        self.data_processor = XeroDataProcessor(base_currency="GBP", industry=IndustryType.GENERAL)
        self.bridge = MCPXeroBridge(organization_id)
        self.import_stats = {
            "accounts": {"total": 0, "imported": 0, "errors": 0},
            "contacts": {"total": 0, "imported": 0, "errors": 0},
            "invoices": {"total": 0, "imported": 0, "errors": 0},
            "transactions": {"total": 0, "imported": 0, "errors": 0},
            "trial_balance": {"fetched": False, "error": None},
            "profit_loss": {"fetched": False, "error": None}
        }
    
    def import_all_data(self, progress_callback=None) -> Dict[str, Any]:
        """
        Import all data from Xero with progress tracking.
        
        Args:
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Dict with import results and statistics
        """
        try:
            logger.info(f"Starting complete data import for organization {self.organization_id}")
            
            # Step 1: Import Organization Details
            self._update_progress(progress_callback, 5, "Fetching organization details...")
            org_details = self._import_organization_details()
            
            # Step 2: Import Chart of Accounts
            self._update_progress(progress_callback, 15, "Importing chart of accounts...")
            accounts_data = self._import_accounts()
            
            # Step 3: Import Contacts
            self._update_progress(progress_callback, 30, "Importing contacts...")
            contacts_data = self._import_contacts()
            
            # Step 4: Import Invoices
            self._update_progress(progress_callback, 50, "Importing invoices...")
            invoices_data = self._import_invoices()
            
            # Step 5: Import Bank Transactions
            self._update_progress(progress_callback, 70, "Importing bank transactions...")
            transactions_data = self._import_bank_transactions()
            
            # Step 6: Import Financial Reports
            self._update_progress(progress_callback, 85, "Importing financial reports...")
            reports_data = self._import_financial_reports()
            
            # Step 7: Process and Validate All Data
            self._update_progress(progress_callback, 95, "Processing and validating data...")
            processed_data = self._process_all_data({
                "organization": org_details,
                "accounts": accounts_data,
                "contacts": contacts_data,
                "invoices": invoices_data,
                "transactions": transactions_data,
                "reports": reports_data
            })
            
            self._update_progress(progress_callback, 100, "Import completed!")
            
            return {
                "success": True,
                "stats": self.import_stats,
                "processed_data": processed_data,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during data import: {e}")
            return {
                "success": False,
                "error": str(e),
                "stats": self.import_stats,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def _update_progress(self, callback, progress: int, message: str):
        """Update progress if callback provided."""
        if callback:
            callback(progress, message)
        logger.info(f"Import progress: {progress}% - {message}")
    
    def _import_organization_details(self) -> Dict[str, Any]:
        """Import organization details from Xero."""
        try:
            org_details = self.mcp_client.get_organization_details()
            logger.info(f"Successfully imported organization: {org_details.get('Name')}")
            return org_details
        except Exception as e:
            logger.error(f"Failed to import organization details: {e}")
            raise
    
    def _import_accounts(self) -> List[Dict[str, Any]]:
        """Import chart of accounts from Xero."""
        try:
            accounts = self.mcp_client.get_accounts()
            self.import_stats["accounts"]["total"] = len(accounts)
            
            # Transform accounts to include GAAP classification
            transformed_accounts = []
            for account in accounts:
                try:
                    # Map to GAAP classification
                    account_code = account.get("Code", "")
                    account_name = account.get("Name", "")
                    mapping_result = self.data_processor.account_mapper.map_account(account_code, account_name)
                    gaap_class = mapping_result.gaap_classification.value if mapping_result else ""
                    
                    # Standardize account data
                    transformed_account = {
                        "xero_account_id": account.get("AccountID"),
                        "code": account.get("Code", ""),
                        "name": account.get("Name", ""),
                        "type": account.get("Type", ""),
                        "tax_type": account.get("TaxType", ""),
                        "description": account.get("Description", ""),
                        "class_type": account.get("Class", ""),
                        "status": account.get("Status", "ACTIVE"),
                        "show_in_expense_claims": account.get("ShowInExpenseClaims", False),
                        "bank_account_number": account.get("BankAccountNumber", ""),
                        "bank_account_type": account.get("BankAccountType", ""),
                        "currency_code": account.get("CurrencyCode", "GBP"),
                        "reporting_code": account.get("ReportingCode", ""),
                        "reporting_code_name": account.get("ReportingCodeName", ""),
                        "has_attachments": account.get("HasAttachments", False),
                        "updated_date_utc": account.get("UpdatedDateUTC", ""),
                        "add_to_watchlist": account.get("AddToWatchlist", False),
                        "gaap_classification": gaap_class
                    }
                    
                    transformed_accounts.append(transformed_account)
                    self.import_stats["accounts"]["imported"] += 1
                    
                except Exception as e:
                    logger.error(f"Error transforming account {account.get('Name')}: {e}")
                    self.import_stats["accounts"]["errors"] += 1
            
            logger.info(f"Imported {len(transformed_accounts)} accounts out of {len(accounts)}")
            return transformed_accounts
            
        except Exception as e:
            logger.error(f"Failed to import accounts: {e}")
            raise
    
    def _import_contacts(self) -> List[Dict[str, Any]]:
        """Import contacts from Xero with enrichment."""
        try:
            # Use direct API call with proper headers
            tokens = self.bridge._get_organization_tokens()
            if not tokens:
                raise Exception("No valid tokens available")
            
            import requests
            headers = {
                'Authorization': f'Bearer {tokens["access_token"]}',
                'Xero-tenant-id': tokens['tenant_id'],
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            response = requests.get('https://api.xero.com/api.xro/2.0/Contacts', headers=headers)
            if response.status_code == 200:
                data = response.json()
                contacts = data.get('Contacts', [])
                self.import_stats["contacts"]["total"] = len(contacts)
                
                # Transform contacts for database (no enrichment)
                transformed_contacts = []
                for contact in contacts:
                    try:
                        transformed_contact = {
                            "xero_contact_id": contact.get("ContactID"),
                            "contact_number": contact.get("ContactNumber", ""),
                            "account_number": contact.get("AccountNumber", ""),
                            "contact_status": contact.get("ContactStatus", "ACTIVE"),
                            "name": contact.get("Name", ""),
                            "first_name": contact.get("FirstName", ""),
                            "last_name": contact.get("LastName", ""),
                            "email_address": contact.get("EmailAddress", ""),
                            "bank_account_details": contact.get("BankAccountDetails", ""),
                            "tax_number": contact.get("TaxNumber", ""),
                            "accounts_receivable_tax_type": contact.get("AccountsReceivableTaxType", ""),
                            "accounts_payable_tax_type": contact.get("AccountsPayableTaxType", ""),
                            "is_supplier": contact.get("IsSupplier", False),
                            "is_customer": contact.get("IsCustomer", False),
                            "default_currency": contact.get("DefaultCurrency", "GBP"),
                            "updated_date_utc": contact.get("UpdatedDateUTC", ""),
                            "has_attachments": contact.get("HasAttachments", False),
                            "has_validation_errors": contact.get("HasValidationErrors", False)
                        }
                        
                        # Add phone numbers if available
                        phones = contact.get("Phones", [])
                        if phones:
                            for phone in phones[:3]:  # Store up to 3 phone numbers
                                phone_type = phone.get("PhoneType", "DEFAULT").lower()
                                transformed_contact[f"phone_{phone_type}"] = phone.get("PhoneNumber", "")
                        
                        # Add addresses if available
                        addresses = contact.get("Addresses", [])
                        if addresses:
                            for address in addresses[:2]:  # Store up to 2 addresses
                                addr_type = address.get("AddressType", "STREET").lower()
                                transformed_contact[f"address_{addr_type}"] = json.dumps({
                                    "line1": address.get("AddressLine1", ""),
                                    "line2": address.get("AddressLine2", ""),
                                    "city": address.get("City", ""),
                                    "region": address.get("Region", ""),
                                    "postal_code": address.get("PostalCode", ""),
                                    "country": address.get("Country", "")
                                })
                        
                        transformed_contacts.append(transformed_contact)
                        self.import_stats["contacts"]["imported"] += 1
                        
                    except Exception as e:
                        logger.error(f"Error transforming contact {contact.get('Name')}: {e}")
                        self.import_stats["contacts"]["errors"] += 1
                
                logger.info(f"Imported {len(transformed_contacts)} contacts")
                return transformed_contacts
                
            else:
                raise Exception(f"API call failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to import contacts: {e}")
            raise
    
    def _import_invoices(self) -> List[Dict[str, Any]]:
        """Import invoices from Xero with detailed line items."""
        try:
            # Use direct API call
            tokens = self.bridge._get_organization_tokens()
            if not tokens:
                raise Exception("No valid tokens available")
            
            import requests
            headers = {
                'Authorization': f'Bearer {tokens["access_token"]}',
                'Xero-tenant-id': tokens['tenant_id'],
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # First, get list of all invoices (this gives us basic info but no line items)
            response = requests.get(
                'https://api.xero.com/api.xro/2.0/Invoices?includeArchived=true',
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                invoices_list = data.get('Invoices', [])
                self.import_stats["invoices"]["total"] = len(invoices_list)
                logger.info(f"Found {len(invoices_list)} invoices to import with detailed line items")
                
                transformed_invoices = []
                for i, basic_invoice in enumerate(invoices_list):
                    try:
                        invoice_id = basic_invoice.get("InvoiceID")
                        if not invoice_id:
                            continue
                        
                        # Get detailed invoice data including line items
                        detail_response = requests.get(
                            f'https://api.xero.com/api.xro/2.0/Invoices/{invoice_id}',
                            headers=headers
                        )
                        
                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()
                            invoice = detail_data.get('Invoices', [{}])[0]
                        else:
                            # Fallback to basic data if detail call fails
                            logger.warning(f"Failed to get details for invoice {invoice_id}, using basic data")
                            invoice = basic_invoice
                        
                        # Progress logging
                        if (i + 1) % 20 == 0:
                            logger.info(f"Processing invoice {i + 1}/{len(invoices_list)}")
                        
                        # Transform invoice data
                        transformed_invoice = {
                            "xero_invoice_id": invoice.get("InvoiceID"),
                            "type": invoice.get("Type", ""),
                            "contact_id": invoice.get("Contact", {}).get("ContactID", ""),
                            "date": invoice.get("Date", ""),
                            "due_date": invoice.get("DueDate", ""),
                            "line_amount_types": invoice.get("LineAmountTypes", ""),
                            "invoice_number": invoice.get("InvoiceNumber", ""),
                            "reference": invoice.get("Reference", ""),
                            "branding_theme_id": invoice.get("BrandingThemeID", ""),
                            "url": invoice.get("Url", ""),
                            "currency_code": invoice.get("CurrencyCode", "GBP"),
                            "currency_rate": float(invoice.get("CurrencyRate", 1.0)),
                            "status": invoice.get("Status", ""),
                            "sent_to_contact": invoice.get("SentToContact", False),
                            "expected_payment_date": invoice.get("ExpectedPaymentDate", ""),
                            "planned_payment_date": invoice.get("PlannedPaymentDate", ""),
                            "sub_total": float(invoice.get("SubTotal", 0)),
                            "total_tax": float(invoice.get("TotalTax", 0)),
                            "total": float(invoice.get("Total", 0)),
                            "total_discount": float(invoice.get("TotalDiscount", 0)),
                            "has_attachments": invoice.get("HasAttachments", False),
                            "has_errors": invoice.get("HasErrors", False),
                            "is_discounted": invoice.get("IsDiscounted", False),
                            "payments": invoice.get("Payments", []),
                            "amount_due": float(invoice.get("AmountDue", 0)),
                            "amount_paid": float(invoice.get("AmountPaid", 0)),
                            "amount_credited": float(invoice.get("AmountCredited", 0)),
                            "updated_date_utc": invoice.get("UpdatedDateUTC", ""),
                            # Store line items as JSON - now will have actual data
                            "line_items": json.dumps(invoice.get("LineItems", []))
                        }
                        
                        transformed_invoices.append(transformed_invoice)
                        self.import_stats["invoices"]["imported"] += 1
                        
                    except Exception as e:
                        logger.error(f"Error transforming invoice {basic_invoice.get('InvoiceNumber', 'Unknown')}: {e}")
                        self.import_stats["invoices"]["errors"] += 1
                
                logger.info(f"Imported {len(transformed_invoices)} invoices with detailed line items")
                return transformed_invoices
                
            else:
                raise Exception(f"API call failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to import invoices: {e}")
            raise
    
    def _import_bank_transactions(self) -> List[Dict[str, Any]]:
        """Import bank transactions from Xero with detailed line items."""
        try:
            # Get transactions for last year
            from_date = datetime.now() - timedelta(days=365)
            to_date = datetime.now()
            
            # Use direct API call
            tokens = self.bridge._get_organization_tokens()
            if not tokens:
                raise Exception("No valid tokens available")
            
            import requests
            headers = {
                'Authorization': f'Bearer {tokens["access_token"]}',
                'Xero-tenant-id': tokens['tenant_id'],
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # First, get list of all bank transactions (this gives us basic info but line items may be limited)
            response = requests.get(
                f'https://api.xero.com/api.xro/2.0/BankTransactions?fromDate={from_date.strftime("%Y-%m-%d")}',
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                transactions_list = data.get('BankTransactions', [])
                self.import_stats["transactions"]["total"] = len(transactions_list)
                logger.info(f"Found {len(transactions_list)} bank transactions to import with detailed line items")
                
                transformed_transactions = []
                for i, basic_transaction in enumerate(transactions_list):
                    try:
                        transaction_id = basic_transaction.get("BankTransactionID")
                        if not transaction_id:
                            continue
                        
                        # Get detailed transaction data including line items
                        detail_response = requests.get(
                            f'https://api.xero.com/api.xro/2.0/BankTransactions/{transaction_id}',
                            headers=headers
                        )
                        
                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()
                            transaction = detail_data.get('BankTransactions', [{}])[0]
                        else:
                            # Fallback to basic data if detail call fails
                            logger.warning(f"Failed to get details for bank transaction {transaction_id}, using basic data")
                            transaction = basic_transaction
                        
                        # Progress logging for large datasets
                        if (i + 1) % 50 == 0:
                            logger.info(f"Processing bank transaction {i + 1}/{len(transactions_list)}")
                        
                        # Transform transaction data
                        transformed_transaction = {
                            "xero_transaction_id": transaction.get("BankTransactionID"),
                            "type": transaction.get("Type", ""),
                            "contact_id": transaction.get("Contact", {}).get("ContactID", "") if transaction.get("Contact") else None,
                            "line_items": json.dumps(transaction.get("LineItems", [])),
                            "bank_account": transaction.get("BankAccount", {}),
                            "is_reconciled": transaction.get("IsReconciled", False),
                            "date": transaction.get("Date", ""),
                            "reference": transaction.get("Reference", ""),
                            "currency_code": transaction.get("CurrencyCode", "GBP"),
                            "currency_rate": float(transaction.get("CurrencyRate", 1.0)),
                            "url": transaction.get("Url", ""),
                            "status": transaction.get("Status", ""),
                            "line_amount_types": transaction.get("LineAmountTypes", ""),
                            "sub_total": float(transaction.get("SubTotal", 0)),
                            "total_tax": float(transaction.get("TotalTax", 0)),
                            "total": float(transaction.get("Total", 0)),
                            "updated_date_utc": transaction.get("UpdatedDateUTC", ""),
                            "has_attachments": transaction.get("HasAttachments", False)
                        }
                        
                        transformed_transactions.append(transformed_transaction)
                        self.import_stats["transactions"]["imported"] += 1
                        
                    except Exception as e:
                        logger.error(f"Error transforming bank transaction {basic_transaction.get('BankTransactionID', 'Unknown')}: {e}")
                        self.import_stats["transactions"]["errors"] += 1
                
                logger.info(f"Imported {len(transformed_transactions)} bank transactions with detailed line items")
                return transformed_transactions
                
            else:
                raise Exception(f"API call failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to import bank transactions: {e}")
            raise
    
    def _import_financial_reports(self) -> Dict[str, Any]:
        """Import financial reports (Trial Balance, P&L)."""
        reports = {}
        
        try:
            # Import Trial Balance
            trial_balance = self.mcp_client.get_trial_balance()
            reports["trial_balance"] = trial_balance
            self.import_stats["trial_balance"]["fetched"] = True
            logger.info("Successfully imported trial balance")
        except Exception as e:
            logger.error(f"Failed to import trial balance: {e}")
            self.import_stats["trial_balance"]["error"] = str(e)
        
        try:
            # Import Profit & Loss (last 12 months)
            from_date = datetime.now() - timedelta(days=365)
            to_date = datetime.now()
            profit_loss = self.mcp_client.get_profit_and_loss(from_date, to_date)
            
            # Process for GAAP compliance
            processed_pl = self.data_processor.process_profit_loss_for_gaap(profit_loss)
            reports["profit_loss"] = processed_pl
            self.import_stats["profit_loss"]["fetched"] = True
            logger.info("Successfully imported profit & loss report")
        except Exception as e:
            logger.error(f"Failed to import profit & loss: {e}")
            self.import_stats["profit_loss"]["error"] = str(e)
        
        return reports
    
    def _process_all_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process all imported data through validation and enrichment pipeline."""
        try:
            # Run enhanced processing pipeline (no enrichment)
            pipeline_result = self.data_processor.process_with_enhanced_pipeline(
                raw_data,
                enable_transformations=True,
                enable_quality_scoring=True,
                enable_duplicate_detection=True,
                batch_size=1000,
                parallel_processing=True
            )
            
            # Add organization and report data
            processed_data = {
                "organization": raw_data.get("organization", {}),
                "accounts": raw_data.get("accounts", []),
                "contacts": raw_data.get("contacts", []),
                "invoices": raw_data.get("invoices", []),
                "transactions": raw_data.get("transactions", []),
                "reports": raw_data.get("reports", {}),
                "processing_results": pipeline_result
            }
            
            logger.info("Data processing completed successfully")
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing data: {e}")
            raise
    
    def import_incremental_data(self, since_date: datetime) -> Dict[str, Any]:
        """Import only data that has changed since the specified date."""
        try:
            logger.info(f"Starting incremental import since {since_date}")
            
            # For incremental sync, we only import invoices and transactions
            # Accounts and contacts are synced in full as they don't change often
            
            invoices_data = self._import_invoices_since(since_date)
            transactions_data = self._import_transactions_since(since_date)
            
            processed_data = self._process_all_data({
                "invoices": invoices_data,
                "transactions": transactions_data
            })
            
            return {
                "success": True,
                "stats": self.import_stats,
                "processed_data": processed_data,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during incremental import: {e}")
            return {
                "success": False,
                "error": str(e),
                "stats": self.import_stats,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def _import_invoices_since(self, since_date: datetime) -> List[Dict[str, Any]]:
        """Import invoices modified since the specified date."""
        # Similar to _import_invoices but with date filter
        # Implementation would add ModifiedSince parameter to API call
        return []
    
    def _import_transactions_since(self, since_date: datetime) -> List[Dict[str, Any]]:
        """Import transactions since the specified date."""
        # Similar to _import_bank_transactions but with date filter
        return []


def test_import_service():
    """Test function for data import service."""
    print("Testing Xero Data Import Service...")
    
    def progress_callback(progress, message):
        print(f"[{progress}%] {message}")
    
    # Use organization ID 2 (Modular CX)
    import_service = XeroDataImportService(organization_id=2)
    
    # Test import
    result = import_service.import_all_data(progress_callback)
    
    if result["success"]:
        print("\n✅ Import completed successfully!")
        print(f"\nImport Statistics:")
        for entity, stats in result["stats"].items():
            if isinstance(stats, dict) and "total" in stats:
                print(f"  {entity}: {stats['imported']}/{stats['total']} imported, {stats['errors']} errors")
    else:
        print(f"\n❌ Import failed: {result['error']}")
    
    return result


if __name__ == "__main__":
    test_import_service()