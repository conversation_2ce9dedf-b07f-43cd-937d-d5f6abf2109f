"""
Enhanced data processing module with GAAP compliance and NASDAQ reporting standards.
"""

from typing import Dict, Optional, Any, List
from decimal import Decimal
import logging
from datetime import datetime
import re
import time

from mcx3d_finance.core.config import get_xero_config
from mcx3d_finance.core.currency_converter import CurrencyConverter
from mcx3d_finance.core.account_mapper import AdvancedAccountMapper, IndustryType
from mcx3d_finance.core.transaction_classifier import TransactionClassifier
# Data enrichment removed - using only real Xero data
from mcx3d_finance.core.duplicate_detector import DuplicateDetector, MergeEngine, ConfidenceLevel
from mcx3d_finance.core.transformation_engine import (
    BatchTransformationEngine, TransformationRule, TransformationRuleType,
    DataQualityScorer
)
from mcx3d_finance.core.validation_integration import (
    IntegratedValidationEngine, ValidationContext, ValidationTrigger,
    ValidationAction, DataRoute
)
from mcx3d_finance.core.account_classifications import GAAPAccountClassification
from mcx3d_finance.core.data_validation import DataValidationEngine

logger = logging.getLogger(__name__)


class XeroDataProcessor:
    """Enhanced Xero data processor with GAAP compliance and NASDAQ standards."""

    def __init__(self, base_currency: str = "USD", industry: IndustryType = IndustryType.GENERAL):
        self.xero_config = get_xero_config()
        self.gaap_mappings = self._load_gaap_account_mappings()
        self.precision = Decimal("0.01")
        self.base_currency = base_currency
        self.industry = industry
        self.currency_converter = CurrencyConverter(base_currency=base_currency)
        self.account_mapper = AdvancedAccountMapper(industry=industry)
        self.transaction_classifier = TransactionClassifier()
        # Data enrichment removed - using only real Xero data
        self.duplicate_detector = DuplicateDetector()
        self.merge_engine = MergeEngine()
        self.transformation_engine = BatchTransformationEngine()
        self.quality_scorer = DataQualityScorer()
        self.validation_engine = DataValidationEngine()
        self.integrated_validation = IntegratedValidationEngine(self.validation_engine)

        self._setup_default_transformation_rules()

    def _load_gaap_account_mappings(self) -> Dict[str, GAAPAccountClassification]:
        """Load GAAP-compliant account mappings for NASDAQ reporting."""
        return {
            "1010": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            "1020": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            "1030": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            "1200": GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            "1210": GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            "1300": GAAPAccountClassification.INVENTORY,
            "1310": GAAPAccountClassification.INVENTORY,
            "1400": GAAPAccountClassification.PREPAID_EXPENSES,
            "1410": GAAPAccountClassification.PREPAID_EXPENSES,
            "1600": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            "1610": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            "1620": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            "1700": GAAPAccountClassification.INTANGIBLE_ASSETS,
            "1710": GAAPAccountClassification.GOODWILL,
            "2000": GAAPAccountClassification.ACCOUNTS_PAYABLE,
            "2010": GAAPAccountClassification.ACCOUNTS_PAYABLE,
            "2100": GAAPAccountClassification.ACCRUED_LIABILITIES,
            "2110": GAAPAccountClassification.ACCRUED_LIABILITIES,
            "2200": GAAPAccountClassification.SHORT_TERM_DEBT,
            "2210": GAAPAccountClassification.CURRENT_PORTION_LONG_TERM_DEBT,
            "2400": GAAPAccountClassification.LONG_TERM_DEBT,
            "2410": GAAPAccountClassification.LONG_TERM_DEBT,
            "3000": GAAPAccountClassification.COMMON_STOCK,
            "3100": GAAPAccountClassification.RETAINED_EARNINGS,
            "3200": GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL,
            "4000": GAAPAccountClassification.PRODUCT_REVENUE,
            "4100": GAAPAccountClassification.SERVICE_REVENUE,
            "4200": GAAPAccountClassification.SUBSCRIPTION_REVENUE,
            "4900": GAAPAccountClassification.OTHER_REVENUE,
            "5000": GAAPAccountClassification.COST_OF_GOODS_SOLD,
            "5100": GAAPAccountClassification.COST_OF_SERVICES,
            "6000": GAAPAccountClassification.SALES_AND_MARKETING,
            "6100": GAAPAccountClassification.RESEARCH_AND_DEVELOPMENT,
            "6200": GAAPAccountClassification.GENERAL_AND_ADMINISTRATIVE,
            "6300": GAAPAccountClassification.DEPRECIATION_AND_AMORTIZATION,
            "7000": GAAPAccountClassification.INTEREST_EXPENSE,
            "7900": GAAPAccountClassification.OTHER_EXPENSE,
        }

    def _setup_default_transformation_rules(self) -> None:
        """Setup default transformation rules for financial data processing."""
        try:
            email_rule = TransformationRule(
                rule_id="normalize_email",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Email Normalization",
                description="Normalize email addresses to lowercase",
                source_fields=["email", "contact_email", "email_address"],
                target_fields=["email"],
                transformation_function="normalize_email",
                priority=10
            )
            self.transformation_engine.add_transformation_rule(email_rule)

            phone_rule = TransformationRule(
                rule_id="normalize_phone",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Phone Normalization",
                description="Normalize phone numbers to standard format",
                source_fields=["phone", "phone_number", "contact_phone"],
                target_fields=["phone"],
                transformation_function="normalize_phone",
                priority=10
            )
            self.transformation_engine.add_transformation_rule(phone_rule)

            currency_rule = TransformationRule(
                rule_id="standardize_currency",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Currency Standardization",
                description="Standardize currency amounts to decimal format",
                source_fields=["amount", "total", "balance", "line_amount"],
                target_fields=["amount"],
                transformation_function="standardize_currency",
                priority=20
            )
            self.transformation_engine.add_transformation_rule(currency_rule)

            date_rule = TransformationRule(
                rule_id="format_date",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Date Formatting",
                description="Format dates to ISO standard",
                source_fields=["date", "created_date", "modified_date", "due_date"],
                target_fields=["date"],
                transformation_function="format_date",
                priority=15
            )
            self.transformation_engine.add_transformation_rule(date_rule)

            text_rule = TransformationRule(
                rule_id="clean_text",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Text Cleaning",
                description="Clean and normalize text fields",
                source_fields=["description", "name", "reference", "notes"],
                target_fields=["description"],
                transformation_function="clean_text",
                priority=5
            )
            self.transformation_engine.add_transformation_rule(text_rule)

            logger.info("Default transformation rules initialized successfully")

        except Exception as e:
            logger.error(f"Error setting up default transformation rules: {e}")





    def validate_account_mappings(
        self,
        accounts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Validate account mappings and provide recommendations."""
        try:
            logger.info("Validating account mappings")

            quality_metrics = self.account_mapper.validate_mapping_quality(accounts)

            validation_results: Dict[str, Any] = {
                "quality_metrics": quality_metrics,
                "validation_checks": [],
                "recommendations": quality_metrics.get("recommendations", [])
            }

            critical_accounts = self._check_critical_accounts(accounts)
            validation_results["validation_checks"].append({
                "check_name": "critical_accounts",
                "passed": len(critical_accounts.get("missing", [])) == 0,
                "details": critical_accounts
            })

            duplicate_check = self._check_duplicate_accounts(accounts)
            validation_results["validation_checks"].append({
                "check_name": "duplicate_accounts",
                "passed": len(duplicate_check.get("duplicates", [])) == 0,
                "details": duplicate_check
            })

            naming_check = self._check_account_naming_consistency(accounts)
            validation_results["validation_checks"].append({
                "check_name": "naming_consistency",
                "passed": naming_check.get("consistency_score", 0) >= 0.8,
                "details": naming_check
            })

            return validation_results

        except Exception as e:
            logger.error(f"Error validating account mappings: {e}")
            return {"error": str(e)}

    def _check_critical_accounts(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check for presence of critical accounts."""
        try:
            critical_patterns: Dict[str, str] = {
                "cash": r"cash|checking|bank",
                "accounts_receivable": r"receivable|a/r|debtors",
                "accounts_payable": r"payable|a/p|creditors",
                "revenue": r"revenue|sales|income",
                "expenses": r"expense|cost|expenditure"
            }

            found_critical: Dict[str, bool] = {}
            for critical_type, pattern in critical_patterns.items():
                found_critical[critical_type] = any(
                    re.search(pattern, account.get("name", ""), re.IGNORECASE) or
                    re.search(pattern, account.get("code", ""), re.IGNORECASE)
                    for account in accounts
                )

            missing = [crit_type for crit_type, found in found_critical.items() if not found]

            return {
                "found_critical": found_critical,
                "missing": missing,
                "critical_account_coverage": (len(found_critical) - len(missing)) / len(found_critical) * 100
            }

        except Exception as e:
            logger.error(f"Error checking critical accounts: {e}")
            return {"error": str(e)}

    def _check_duplicate_accounts(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check for duplicate account codes or names."""
        try:
            codes_seen: Dict[str, Dict[str, Any]] = {}
            names_seen: Dict[str, Dict[str, Any]] = {}
            duplicates: List[Dict[str, Any]] = []

            for account in accounts:
                code = account.get("code", "")
                name = account.get("name", "")

                # Check code duplicates
                if code and code in codes_seen:
                    duplicates.append({
                        "type": "code",
                        "value": code,
                        "accounts": [codes_seen[code], account]
                    })
                else:
                    codes_seen[code] = account

                # Check name duplicates (case-insensitive)
                name_lower = name.lower()
                if name_lower and name_lower in names_seen:
                    duplicates.append({
                        "type": "name",
                        "value": name,
                        "accounts": [names_seen[name_lower], account]
                    })
                else:
                    names_seen[name_lower] = account

            return {
                "duplicates": duplicates,
                "duplicate_count": len(duplicates),
                "total_accounts": len(accounts)
            }

        except Exception as e:
            logger.error(f"Error checking duplicate accounts: {e}")
            return {"error": str(e)}

    def _check_account_naming_consistency(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check account naming consistency and conventions."""
        try:
            naming_issues = []
            total_accounts = len(accounts)

            for account in accounts:
                name = account.get("name", "")

                # Check for empty names
                if not name.strip():
                    naming_issues.append({
                        "account": account,
                        "issue": "empty_name",
                        "description": "Account has empty or whitespace-only name"
                    })

                # Check for very short names (less than 3 characters)
                if len(name.strip()) < 3:
                    naming_issues.append({
                        "account": account,
                        "issue": "short_name",
                        "description": "Account name is very short (less than 3 characters)"
                    })

                # Check for inconsistent capitalization
                if name != name.title() and name != name.upper() and name != name.lower():
                    naming_issues.append({
                        "account": account,
                        "issue": "inconsistent_capitalization",
                        "description": "Account name has inconsistent capitalization"
                    })

                # Check for special characters in names
                if re.search(r'[^\w\s\-\(\)&]', name):
                    naming_issues.append({
                        "account": account,
                        "issue": "special_characters",
                        "description": "Account name contains unusual special characters"
                    })

            consistency_score = max(0, (total_accounts - len(naming_issues)) / total_accounts) if total_accounts > 0 else 1

            return {
                "naming_issues": naming_issues,
                "issue_count": len(naming_issues),
                "total_accounts": total_accounts,
                "consistency_score": consistency_score
            }

        except Exception as e:
            logger.error(f"Error checking account naming consistency: {e}")
            return {"error": str(e)}

    def detect_and_merge_duplicates(
        self,
        financial_data: Dict[str, Any],
        auto_merge_high_confidence: bool = True,
        batch_size: int = 1000
    ) -> Dict[str, Any]:
        """Detect and optionally merge duplicate entities in financial data."""
        try:
            logger.info("Starting sophisticated duplicate detection and merging")

            result: Dict[str, Any] = {
                "duplicates_detected": {},
                "merges_performed": {},
                "conflicts_flagged": {},
                "processing_stats": {}
            }

            # Process each entity type
            for entity_type in ['transactions', 'contacts', 'accounts']:
                if entity_type in financial_data:
                    entities = financial_data[entity_type]
                    logger.info(f"Processing {len(entities)} {entity_type} for duplicates")

                    # Detect duplicates
                    duplicates = self.duplicate_detector.detect_duplicates(
                        entities, entity_type.rstrip('s'), batch_size
                    )

                    result["duplicates_detected"][entity_type] = {
                        "total_found": len(duplicates),
                        "high_confidence": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.HIGH]),
                        "medium_confidence": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.MEDIUM]),
                        "low_confidence": len([d for d in duplicates if d.confidence_level == ConfidenceLevel.LOW])
                    }

                    # Auto-merge high confidence duplicates if enabled
                    merges_performed = []
                    conflicts_flagged = []

                    if auto_merge_high_confidence:
                        high_confidence_duplicates = [
                            d for d in duplicates if d.confidence_level == ConfidenceLevel.HIGH
                        ]

                        for duplicate in high_confidence_duplicates:
                            # Find the actual entities
                            entity1 = next((e for e in entities if e.get('id') == duplicate.entity1_id), None)
                            entity2 = next((e for e in entities if e.get('id') == duplicate.entity2_id), None)

                            if entity1 and entity2:
                                merge_result = self.merge_engine.merge_duplicates(duplicate, entity1, entity2)
                                if merge_result.success:
                                    merges_performed.append({
                                        "merged_id": merge_result.merged_entity_id,
                                        "source_ids": merge_result.source_entity_ids,
                                        "confidence": duplicate.confidence_score,
                                        "conflicts_resolved": len(merge_result.conflicts_resolved)
                                    })

                                    # Update the entities list
                                    entities = [e for e in entities if e.get('id') not in merge_result.source_entity_ids]
                                    entities.append(merge_result.merged_fields)
                                else:
                                    logger.error(f"Failed to merge entities: {merge_result.error_message}")

                    # Flag medium confidence duplicates for review
                    medium_confidence_duplicates = [
                        d for d in duplicates if d.confidence_level == ConfidenceLevel.MEDIUM
                    ]

                    for duplicate in medium_confidence_duplicates:
                        conflicts_flagged.append({
                            "entity_type": entity_type,
                            "entity1_id": duplicate.entity1_id,
                            "entity2_id": duplicate.entity2_id,
                            "confidence": duplicate.confidence_score,
                            "matching_fields": duplicate.matching_fields,
                            "recommendation": duplicate.merge_recommendation
                        })

                    result["merges_performed"][entity_type] = merges_performed
                    result["conflicts_flagged"][entity_type] = conflicts_flagged

                    # Update the financial data with processed entities
                    financial_data[entity_type] = entities

                    result["processing_stats"][entity_type] = {
                        "original_count": len(financial_data[entity_type]) + len(merges_performed),
                        "final_count": len(entities),
                        "duplicates_merged": len(merges_performed),
                        "conflicts_flagged": len(conflicts_flagged)
                    }

            logger.info("Duplicate detection and merging completed successfully")
            return result

        except Exception as e:
            logger.error(f"Error in duplicate detection and merging: {e}")
            return {"error": str(e)}

    def process_with_enhanced_pipeline(
        self,
        financial_data: Dict[str, Any],
        enable_transformations: bool = True,
        enable_quality_scoring: bool = True,
        enable_duplicate_detection: bool = True,
        batch_size: int = 1000,
        parallel_processing: bool = True
    ) -> Dict[str, Any]:
        """Process financial data through enhanced pipeline with transformations, quality scoring, and duplicate detection."""
        try:
            logger.info("Starting enhanced data processing pipeline")

            pipeline_result: Dict[str, Any] = {
                "original_data_stats": {},
                "transformation_results": {},
                "quality_reports": {},
                "duplicate_detection_results": {},
                "final_data_stats": {},
                "processing_metadata": {
                    "pipeline_version": "2.0",
                    "processing_timestamp": datetime.utcnow().isoformat(),
                    "features_enabled": {
                        "transformations": enable_transformations,
                        "quality_scoring": enable_quality_scoring,
                        "duplicate_detection": enable_duplicate_detection
                    }
                }
            }

            for data_type in ['transactions', 'contacts', 'accounts']:
                if data_type in financial_data:
                    data_list = financial_data[data_type]
                    pipeline_result["original_data_stats"][data_type] = {
                        "record_count": len(data_list),
                        "field_count": len(data_list[0].keys()) if data_list else 0
                    }

            if enable_transformations:
                logger.info("Applying data transformations")
                for data_type in ['transactions', 'contacts', 'accounts']:
                    if data_type in financial_data:
                        data_list = financial_data[data_type]
                        if data_list:
                            batch_result = self.transformation_engine.process_batch(
                                data_list,
                                data_type.rstrip('s'),
                                batch_size=batch_size,
                                parallel=parallel_processing
                            )
                            pipeline_result["transformation_results"][data_type] = {
                                "batch_id": batch_result.batch_id,
                                "status": batch_result.status.value,
                                "processed_records": batch_result.processed_records,
                                "successful_records": batch_result.successful_records,
                                "failed_records": batch_result.failed_records,
                                "processing_time": batch_result.processing_time,
                                "quality_score": batch_result.overall_quality_score
                            }

            if enable_quality_scoring:
                logger.info("Generating data quality reports")
                for data_type in ['transactions', 'contacts', 'accounts']:
                    if data_type in financial_data:
                        data_list = financial_data[data_type]
                        if data_list:
                            quality_report = self.quality_scorer.calculate_quality_score(data_list)
                            pipeline_result["quality_reports"][data_type] = quality_report

            if enable_duplicate_detection:
                logger.info("Detecting and merging duplicates")
                duplicate_results = self.detect_and_merge_duplicates(
                    financial_data,
                    auto_merge_high_confidence=True,
                    batch_size=batch_size
                )
                pipeline_result["duplicate_detection_results"] = duplicate_results

            for data_type in ['transactions', 'contacts', 'accounts']:
                if data_type in financial_data:
                    data_list = financial_data[data_type]
                    pipeline_result["final_data_stats"][data_type] = {
                        "record_count": len(data_list),
                        "field_count": len(data_list[0].keys()) if data_list else 0
                    }

            total_original_records = sum(
                stats.get("record_count", 0)
                for stats in pipeline_result["original_data_stats"].values()
            )
            total_final_records = sum(
                stats.get("record_count", 0)
                for stats in pipeline_result["final_data_stats"].values()
            )

            pipeline_result["processing_metadata"]["overall_metrics"] = {
                "total_original_records": total_original_records,
                "total_final_records": total_final_records,
                "records_processed": total_original_records,
                "data_reduction_ratio": (total_original_records - total_final_records) / total_original_records if total_original_records > 0 else 0.0
            }

            logger.info(f"Enhanced pipeline processing completed. "
                       f"Processed {total_original_records} records, "
                       f"final count: {total_final_records}")

            return pipeline_result

        except Exception as e:
            logger.error(f"Error in enhanced data processing pipeline: {e}")
            return {
                "error": str(e),
                "processing_metadata": {
                    "pipeline_version": "2.0",
                    "processing_timestamp": datetime.utcnow().isoformat(),
                    "status": "failed"
                }
            }

    async def process_with_integrated_validation(
        self,
        financial_data: Dict[str, Any],
        organization_id: str,
        enable_real_time_validation: bool = False,
        enable_transformation_validation: bool = True,
        enable_sync_validation: bool = True,
        batch_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process financial data with integrated validation at each step."""
        try:
            logger.info("Starting integrated validation processing pipeline")

            pipeline_result: Dict[str, Any] = {
                "ingestion_validation": {},
                "transformation_validation": {},
                "sync_validation": {},
                "final_routing": {},
                "processing_metadata": {
                    "pipeline_version": "3.0_integrated_validation",
                    "processing_timestamp": datetime.utcnow().isoformat(),
                    "organization_id": organization_id,
                    "batch_id": batch_id or f"batch_{int(time.time())}"
                }
            }

            logger.info("Step 1: Performing ingestion validation")
            ingestion_context = ValidationContext(
                organization_id=organization_id,
                data_type="mixed",
                trigger=ValidationTrigger.REAL_TIME if enable_real_time_validation else ValidationTrigger.ON_INGESTION,
                batch_id=str(pipeline_result["processing_metadata"]["batch_id"]),
                source_system="data_processor",
                metadata={"step": "ingestion", "record_count": sum(len(v) if isinstance(v, list) else 1 for v in financial_data.values())}
            )

            ingestion_result = await self.integrated_validation.validate_and_route_data(
                financial_data, ingestion_context, enable_routing=True
            )

            pipeline_result["ingestion_validation"] = {
                "action": ingestion_result.action.value,
                "route": ingestion_result.route.value,
                "policy_applied": ingestion_result.policy_applied,
                "validation_passed": ingestion_result.validation_report.passed_checks,
                "validation_failed": ingestion_result.validation_report.failed_checks,
                "processing_time": ingestion_result.processing_time
            }

            if ingestion_result.action == ValidationAction.REJECT:
                logger.warning("Data rejected at ingestion validation step")
                pipeline_result["final_status"] = "rejected_at_ingestion"
                return pipeline_result

            if enable_transformation_validation:
                logger.info("Step 2: Applying transformations with validation")

                original_data = {k: v.copy() if isinstance(v, list) else v for k, v in financial_data.items()}

                enhanced_result = self.process_with_enhanced_pipeline(
                    financial_data,
                    enable_transformations=True,
                    enable_quality_scoring=True,
                    enable_duplicate_detection=True,
                    batch_size=1000,
                    parallel_processing=True
                )

                transform_context = ValidationContext(
                    organization_id=organization_id,
                    data_type="mixed",
                    trigger=ValidationTrigger.ON_TRANSFORMATION,
                    batch_id=str(pipeline_result["processing_metadata"]["batch_id"]),
                    source_system="transformation_engine",
                    metadata={"step": "transformation", "enhanced_result": enhanced_result}
                )

                transformation_result = self.integrated_validation.validate_during_transformation(
                    original_data, financial_data, transform_context
                )

                pipeline_result["transformation_validation"] = {
                    "action": transformation_result.action.value,
                    "route": transformation_result.route.value,
                    "policy_applied": transformation_result.policy_applied,
                    "validation_passed": transformation_result.validation_report.passed_checks,
                    "validation_failed": transformation_result.validation_report.failed_checks,
                    "processing_time": transformation_result.processing_time,
                    "enhancement_results": enhanced_result
                }

                if transformation_result.action == ValidationAction.REJECT:
                    logger.warning("Data rejected at transformation validation step")
                    pipeline_result["final_status"] = "rejected_at_transformation"
                    return pipeline_result

            if enable_sync_validation:
                logger.info("Step 3: Performing sync validation")

                sync_context = ValidationContext(
                    organization_id=organization_id,
                    data_type="mixed",
                    trigger=ValidationTrigger.ON_SYNC,
                    batch_id=str(pipeline_result["processing_metadata"]["batch_id"]),
                    source_system="xero_sync",
                    metadata={"step": "sync_preparation"}
                )

                sync_result = self.integrated_validation.validate_during_sync(
                    financial_data, sync_context
                )

                pipeline_result["sync_validation"] = {
                    "action": sync_result.action.value,
                    "route": sync_result.route.value,
                    "policy_applied": sync_result.policy_applied,
                    "validation_passed": sync_result.validation_report.passed_checks,
                    "validation_failed": sync_result.validation_report.failed_checks,
                    "processing_time": sync_result.processing_time
                }

                pipeline_result["final_routing"] = {
                    "final_action": sync_result.action.value,
                    "final_route": sync_result.route.value,
                    "ready_for_sync": sync_result.action in [ValidationAction.ACCEPT, ValidationAction.QUARANTINE]
                }

            validation_stats = self.integrated_validation.get_processing_statistics()
            pipeline_result["processing_metadata"]["validation_statistics"] = validation_stats

            if pipeline_result.get("final_routing", {}).get("ready_for_sync", False):
                pipeline_result["final_status"] = "ready_for_sync"
            else:
                pipeline_result["final_status"] = "requires_review"

            logger.info(f"Integrated validation pipeline completed: {pipeline_result['final_status']}")
            return pipeline_result

        except Exception as e:
            logger.error(f"Error in integrated validation processing: {e}")
            return {
                "error": str(e),
                "final_status": "processing_error",
                "processing_metadata": {
                    "pipeline_version": "3.0_integrated_validation",
                    "processing_timestamp": datetime.utcnow().isoformat(),
                    "organization_id": organization_id,
                    "error_occurred": True
                }
            }

    def validate_real_time_data(
        self,
        record: Dict[str, Any],
        data_type: str,
        organization_id: str
    ) -> Dict[str, Any]:
        """Validate a single record in real-time."""
        try:
            context = ValidationContext(
                organization_id=organization_id,
                data_type=data_type,
                trigger=ValidationTrigger.REAL_TIME,
                source_system="real_time_stream",
                metadata={"real_time_validation": True}
            )

            import asyncio

            async def run_validation():
                return await self.integrated_validation.validate_and_route_data(
                    {data_type + 's': [record]}, context, enable_routing=True
                )

            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            result = loop.run_until_complete(run_validation())

            return {
                "validation_passed": result.validation_report.passed_checks > 0,
                "action": result.action.value,
                "route": result.route.value,
                "policy_applied": result.policy_applied,
                "processing_time": result.processing_time,
                "validation_details": {
                    "total_checks": result.validation_report.total_checks,
                    "passed_checks": result.validation_report.passed_checks,
                    "failed_checks": result.validation_report.failed_checks
                }
            }

        except Exception as e:
            logger.error(f"Error in real-time validation: {e}")
            return {
                "validation_passed": False,
                "action": ValidationAction.REJECT.value,
                "route": DataRoute.INVALID_QUEUE.value,
                "error": str(e)
            }
    def process_profit_loss_for_gaap(self, profit_loss_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process profit & loss data for GAAP compliance"""
        try:
            # Basic processing for now - can be enhanced later
            processed_data = {
                "report_date": profit_loss_data.get("report_date"),
                "sections": [],
                "gaap_compliant": True
            }
            
            for section in profit_loss_data.get("sections", []):
                processed_section = {
                    "section_name": section.get("section_name", ""),
                    "accounts": [],
                    "total": 0.0
                }
                
                for account in section.get("accounts", []):
                    processed_account = {
                        "account_name": account.get("account_name", ""),
                        "amount": float(account.get("amount", 0)),
                        "gaap_classification": self._classify_for_gaap(account.get("account_name", ""))
                    }
                    processed_section["accounts"].append(processed_account)
                    processed_section["total"] += processed_account["amount"]
                
                processed_data["sections"].append(processed_section)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing profit & loss for GAAP: {e}")
            return profit_loss_data
            
    def _classify_for_gaap(self, account_name: str) -> str:
        """Basic GAAP classification helper"""
        account_lower = account_name.lower()
        
        if any(term in account_lower for term in ['revenue', 'sales', 'income']):
            return 'revenue'
        elif any(term in account_lower for term in ['expense', 'cost', 'depreciation']):
            return 'expense'
        else:
            return 'other'

