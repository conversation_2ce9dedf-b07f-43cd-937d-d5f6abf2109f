import logging
from typing import Dict, Any, Optional
from datetime import datetime
from decimal import Decimal
from sqlalchemy import and_

from mcx3d_finance.db.session import SessionLocal
from mcx3d_finance.db.models import Organization, Account, BankTransaction
from mcx3d_finance.core.financials.gaap_categories import GAAPAccountClassification

logger = logging.getLogger(__name__)


class BalanceSheetGenerator:
    """Generate NASDAQ-compliant balance sheets following GAAP standards."""

    def __init__(self, organization_id: int):
        self.organization_id = organization_id
        self.precision = Decimal("0.01")
        self.db = SessionLocal()

    def generate_balance_sheet(
        self, as_of_date: datetime, comparative_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Generate a balance sheet for the given organization."""
        try:
            organization = self.db.query(Organization).get(self.organization_id)
            if not organization:
                raise ValueError("Organization not found")

            gaap_data = self._calculate_account_balances(as_of_date)

            comparative_gaap_data = None
            if comparative_date:
                comparative_gaap_data = self._calculate_account_balances(
                    comparative_date
                )

            nasdaq_report = self._format_for_nasdaq(
                gaap_data, as_of_date, comparative_gaap_data, comparative_date
            )

            analysis = self._generate_balance_sheet_analysis(gaap_data)
            nasdaq_report["financial_analysis"] = analysis

            certifications = self._add_compliance_certifications()
            nasdaq_report["compliance_certifications"] = certifications

            return nasdaq_report
        except Exception as e:
            logger.error(f"Failed to generate balance sheet: {e}", exc_info=True)
            return {"error": "Failed to generate balance sheet.", "detail": str(e)}
        finally:
            if self.db:
                self.db.close()

    def _calculate_account_balances(self, as_of_date: datetime) -> Dict[str, Any]:
        """Calculate account balances from database transactions."""
        try:
            # Get all accounts for the organization
            accounts = (
                self.db.query(Account)
                .filter(
                    Account.organization_id == self.organization_id,
                    Account.status == "ACTIVE",
                )
                .all()
            )

            # Initialize GAAP structure
            gaap_structure = {
                "assets": {
                    "current_assets": {},
                    "non_current_assets": {},
                    "total_assets": 0,
                },
                "liabilities": {
                    "current_liabilities": {},
                    "non_current_liabilities": {},
                    "total_liabilities": 0,
                },
                "equity": {"stockholders_equity": {}, "total_equity": 0},
            }

            # Calculate balances for each account
            for account in accounts:
                # Get all transactions for this account up to the as_of_date
                transactions = (
                    self.db.query(BankTransaction)
                    .filter(
                        and_(
                            BankTransaction.organization_id == self.organization_id,
                            BankTransaction.date <= as_of_date,
                        )
                    )
                    .all()
                )

                # Calculate balance
                balance = 0
                for transaction in transactions:
                    # Check if this transaction is for this account
                    if transaction.bank_account and isinstance(
                        transaction.bank_account, dict
                    ):
                        if (
                            transaction.bank_account.get("AccountID")
                            == account.xero_account_id
                        ):
                            if transaction.type == "RECEIVE":
                                balance += float(transaction.total)
                            else:
                                balance -= float(transaction.total)

                # Classify account based on GAAP
                if account.gaap_classification:
                    classification = account.gaap_classification

                    # Map to appropriate section
                    if classification in [
                        GAAPAccountClassification.CASH_AND_EQUIVALENTS.value,
                        GAAPAccountClassification.ACCOUNTS_RECEIVABLE.value,
                        GAAPAccountClassification.INVENTORY.value,
                        GAAPAccountClassification.PREPAID_EXPENSES.value,
                    ]:
                        gaap_structure["assets"]["current_assets"][
                            account.name
                        ] = balance

                    elif classification in [
                        GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT.value,
                        GAAPAccountClassification.INTANGIBLE_ASSETS.value,
                        GAAPAccountClassification.GOODWILL.value,
                    ]:
                        gaap_structure["assets"]["non_current_assets"][
                            account.name
                        ] = balance

                    elif classification in [
                        GAAPAccountClassification.ACCOUNTS_PAYABLE.value,
                        GAAPAccountClassification.ACCRUED_LIABILITIES.value,
                        GAAPAccountClassification.SHORT_TERM_DEBT.value,
                        GAAPAccountClassification.CURRENT_PORTION_LONG_TERM_DEBT.value,
                    ]:
                        gaap_structure["liabilities"]["current_liabilities"][
                            account.name
                        ] = abs(balance)

                    elif classification in [GAAPAccountClassification.LONG_TERM_DEBT.value]:
                        gaap_structure["liabilities"]["non_current_liabilities"][
                            account.name
                        ] = abs(balance)

                    elif classification in [
                        GAAPAccountClassification.COMMON_STOCK.value,
                        GAAPAccountClassification.RETAINED_EARNINGS.value,
                        GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL.value,
                    ]:
                        gaap_structure["equity"]["stockholders_equity"][
                            account.name
                        ] = abs(balance)

            # Calculate totals
            gaap_structure["assets"]["total_assets"] = sum(
                gaap_structure["assets"]["current_assets"].values()
            ) + sum(gaap_structure["assets"]["non_current_assets"].values())

            gaap_structure["liabilities"]["total_liabilities"] = sum(
                gaap_structure["liabilities"]["current_liabilities"].values()
            ) + sum(gaap_structure["liabilities"]["non_current_liabilities"].values())

            gaap_structure["equity"]["total_equity"] = sum(
                gaap_structure["equity"]["stockholders_equity"].values()
            )

            return gaap_structure

        except Exception as e:
            logger.error(f"Error calculating account balances: {e}")
            raise

    def _format_for_nasdaq(
        self,
        gaap_data: Dict[str, Any],
        as_of_date: datetime,
        comparative_data: Optional[Dict[str, Any]] = None,
        comparative_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """Format balance sheet data for NASDAQ presentation standards."""

        nasdaq_format = {
            "header": {
                "company_name": "MCX3D Corporation",  # Should come from organization settings
                "statement_title": "CONSOLIDATED BALANCE SHEETS",
                "reporting_date": as_of_date.strftime("%B %d, %Y"),
                "comparative_date": (
                    comparative_date.strftime("%B %d, %Y")
                    if comparative_date
                    else None
                ),
                "currency": "USD",
                "amounts_in": "thousands",  # NASDAQ standard
                "prepared_in_accordance_with": "U.S. Generally Accepted Accounting Principles (GAAP)",
            },
            "assets": {
                "current_assets": {
                    "title": "CURRENT ASSETS:",
                    "cash_and_cash_equivalents": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["assets"][
                                        "current_assets"
                                    ].items()
                                    if "cash" in k.lower() or "bank" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data, "assets", "current_assets", "cash"
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "accounts_receivable_net": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["assets"][
                                        "current_assets"
                                    ].items()
                                    if "receivable" in k.lower()
                                    or "debtors" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "assets",
                                "current_assets",
                                "receivable",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "inventory": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["assets"][
                                        "current_assets"
                                    ].items()
                                    if "inventory" in k.lower() or "stock" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "assets",
                                "current_assets",
                                "inventory",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "prepaid_expenses_and_other": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["assets"][
                                        "current_assets"
                                    ].items()
                                    if "prepaid" in k.lower() or "other" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data, "assets", "current_assets", "prepaid"
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "total_current_assets": {
                        "current": self._format_amount(
                            sum(gaap_data["assets"]["current_assets"].values())
                        ),
                        "comparative": (
                            self._format_amount(
                                sum(
                                    comparative_data["assets"][
                                        "current_assets"
                                    ].values()
                                )
                            )
                            if comparative_data
                            else None
                        ),
                    },
                },
                "non_current_assets": {
                    "title": "NON-CURRENT ASSETS:",
                    "property_plant_equipment_net": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["assets"][
                                        "non_current_assets"
                                    ].items()
                                    if any(
                                        term in k.lower()
                                        for term in [
                                            "property",
                                            "plant",
                                            "equipment",
                                            "fixed",
                                        ]
                                    )
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "assets",
                                "non_current_assets",
                                "property",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "intangible_assets_net": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["assets"][
                                        "non_current_assets"
                                    ].items()
                                    if "intangible" in k.lower()
                                    or "software" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "assets",
                                "non_current_assets",
                                "intangible",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "goodwill": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["assets"][
                                        "non_current_assets"
                                    ].items()
                                    if "goodwill" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "assets",
                                "non_current_assets",
                                "goodwill",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "other_non_current_assets": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["assets"][
                                        "non_current_assets"
                                    ].items()
                                    if not any(
                                        term in k.lower()
                                        for term in [
                                            "property",
                                            "plant",
                                            "equipment",
                                            "intangible",
                                            "goodwill",
                                        ]
                                    )
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "assets",
                                "non_current_assets",
                                "other",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "total_non_current_assets": {
                        "current": self._format_amount(
                            sum(gaap_data["assets"]["non_current_assets"].values())
                        ),
                        "comparative": (
                            self._format_amount(
                                sum(
                                    comparative_data["assets"][
                                        "non_current_assets"
                                    ].values()
                                )
                            )
                            if comparative_data
                            else None
                        ),
                    },
                },
                "total_assets": {
                    "current": self._format_amount(gaap_data["assets"]["total_assets"]),
                    "comparative": (
                        self._format_amount(comparative_data["assets"]["total_assets"])
                        if comparative_data
                        else None
                    ),
                },
            },
            "liabilities_and_equity": {
                "current_liabilities": {
                    "title": "CURRENT LIABILITIES:",
                    "accounts_payable": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["liabilities"][
                                        "current_liabilities"
                                    ].items()
                                    if "payable" in k.lower()
                                    or "creditors" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "liabilities",
                                "current_liabilities",
                                "payable",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "accrued_liabilities": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["liabilities"][
                                        "current_liabilities"
                                    ].items()
                                    if "accrued" in k.lower() or "accrual" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "liabilities",
                                "current_liabilities",
                                "accrued",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "short_term_debt": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["liabilities"][
                                        "current_liabilities"
                                    ].items()
                                    if any(
                                        term in k.lower()
                                        for term in [
                                            "short",
                                            "debt",
                                            "loan",
                                            "borrowing",
                                        ]
                                    )
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "liabilities",
                                "current_liabilities",
                                "debt",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "total_current_liabilities": {
                        "current": self._format_amount(
                            sum(
                                gaap_data["liabilities"]["current_liabilities"].values()
                            )
                        ),
                        "comparative": (
                            self._format_amount(
                                sum(
                                    comparative_data["liabilities"][
                                        "current_liabilities"
                                    ].values()
                                )
                            )
                            if comparative_data
                            else None
                        ),
                    },
                },
                "non_current_liabilities": {
                    "title": "NON-CURRENT LIABILITIES:",
                    "long_term_debt": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["liabilities"][
                                        "non_current_liabilities"
                                    ].items()
                                    if any(
                                        term in k.lower()
                                        for term in ["long", "debt", "loan"]
                                    )
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "liabilities",
                                "non_current_liabilities",
                                "debt",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "deferred_tax_liabilities": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["liabilities"][
                                        "non_current_liabilities"
                                    ].items()
                                    if "tax" in k.lower() and "deferred" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "liabilities",
                                "non_current_liabilities",
                                "tax",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "total_non_current_liabilities": {
                        "current": self._format_amount(
                            sum(
                                gaap_data["liabilities"][
                                    "non_current_liabilities"
                                ].values()
                            )
                        ),
                        "comparative": (
                            self._format_amount(
                                sum(
                                    comparative_data["liabilities"][
                                        "non_current_liabilities"
                                    ].values()
                                )
                            )
                            if comparative_data
                            else None
                        ),
                    },
                },
                "total_liabilities": {
                    "current": self._format_amount(
                        gaap_data["liabilities"]["total_liabilities"]
                    ),
                    "comparative": (
                        self._format_amount(
                            comparative_data["liabilities"]["total_liabilities"]
                        )
                        if comparative_data
                        else None
                    ),
                },
                "stockholders_equity": {
                    "title": "STOCKHOLDERS' EQUITY:",
                    "common_stock": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["equity"][
                                        "stockholders_equity"
                                    ].items()
                                    if "common" in k.lower()
                                    or "share capital" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "equity",
                                "stockholders_equity",
                                "common",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "additional_paid_in_capital": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["equity"][
                                        "stockholders_equity"
                                    ].items()
                                    if "additional" in k.lower()
                                    or "premium" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "equity",
                                "stockholders_equity",
                                "additional",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "retained_earnings": {
                        "current": self._format_amount(
                            sum(
                                [
                                    v
                                    for k, v in gaap_data["equity"][
                                        "stockholders_equity"
                                    ].items()
                                    if "retained" in k.lower()
                                    or "accumulated" in k.lower()
                                ]
                            )
                        ),
                        "comparative": (
                            self._get_comparative_amount(
                                comparative_data,
                                "equity",
                                "stockholders_equity",
                                "retained",
                            )
                            if comparative_data
                            else None
                        ),
                    },
                    "total_stockholders_equity": {
                        "current": self._format_amount(
                            gaap_data["equity"]["total_equity"]
                        ),
                        "comparative": (
                            self._format_amount(
                                comparative_data["equity"]["total_equity"]
                            )
                            if comparative_data
                            else None
                        ),
                    },
                },
                "total_liabilities_and_equity": {
                    "current": self._format_amount(
                        gaap_data["liabilities"]["total_liabilities"]
                        + gaap_data["equity"]["total_equity"]
                    ),
                    "comparative": (
                        self._format_amount(
                            comparative_data["liabilities"]["total_liabilities"]
                            + comparative_data["equity"]["total_equity"]
                        )
                        if comparative_data
                        else None
                    ),
                },
            },
        }

        return nasdaq_format

    def _format_amount(self, amount: float) -> int:
        """Format amount in thousands for NASDAQ presentation."""
        return int(round(amount / 1000))

    def _get_comparative_amount(
        self,
        comparative_data: Dict[str, Any],
        section: str,
        subsection: str,
        keyword: str,
    ) -> Optional[int]:
        """Get comparative amount for specific account type."""
        if not comparative_data:
            return None

        try:
            accounts = comparative_data[section][subsection]
            matching_amount = sum(
                [v for k, v in accounts.items() if keyword.lower() in k.lower()]
            )
            return self._format_amount(matching_amount)
        except (KeyError, TypeError):
            return None

    def _generate_balance_sheet_analysis(
        self, gaap_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate financial analysis based on balance sheet data."""
        total_assets = gaap_data["assets"]["total_assets"]
        total_liabilities = gaap_data["liabilities"]["total_liabilities"]
        total_equity = gaap_data["equity"]["total_equity"]

        return {
            "key_metrics": {
                "total_assets": self._format_amount(total_assets),
                "total_liabilities": self._format_amount(total_liabilities),
                "total_equity": self._format_amount(total_equity),
                "debt_to_equity_ratio": (
                    self._format_amount(total_liabilities / total_equity)
                    if total_equity > 0
                    else "N/A"
                ),
            },
            "summary": "This is an auto-generated summary of the balance sheet.",
            "prepared_by": "MCX3D Finance Engine",
            "preparation_date": datetime.utcnow().isoformat(),
        }

    def _add_compliance_certifications(self) -> Dict[str, str]:
        """Add compliance certifications to the report."""
        return {
            "gaap_compliance": "This report is believed to be in compliance with U.S. GAAP.",
            "nasdaq_compliance_statement": "This report is formatted in accordance with NASDAQ reporting standards.",
        }
