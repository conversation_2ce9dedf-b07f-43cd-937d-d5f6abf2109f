"""
Advanced Financial Projection Builder using historical Xero data.

This module creates sophisticated financial projections by analyzing historical
trends, seasonal patterns, and business growth metrics from Xero data.
"""

from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime, timedelta
import pandas as pd
import logging
import numpy as np
from sqlalchemy.orm import Session

from ...db.models import Organization, Invoice, Contact, BankTransaction
from ...core.financials.income_statement import IncomeStatementGenerator
from ...core.financials.balance_sheet import BalanceSheetGenerator
from ...core.metrics.saas_kpis import SaaSKPICalculator

logger = logging.getLogger(__name__)


class FinancialProjectionBuilder:
    """
    Advanced financial projection builder that analyzes historical Xero data
    to create data-driven financial forecasts for DCF valuations.
    """
    
    def __init__(self, db: Session):
        self.db = db
        
    def build_comprehensive_projections(self, 
                                      organization_id: int, 
                                      projection_years: int = 5,
                                      historical_years: int = 3,
                                      scenarios: Optional[List[str]] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Build comprehensive financial projections with multiple scenarios.
        
        Args:
            organization_id: Xero organization ID
            projection_years: Number of years to project
            historical_years: Years of historical data to analyze
            scenarios: List of scenarios ['base', 'upside', 'downside']
            
        Returns:
            Dictionary with scenario-based projections
        """
        try:
            if scenarios is None:
                scenarios = ['base', 'upside', 'downside']
                
            logger.info(f"Building comprehensive projections for org {organization_id}")
            
            # Analyze historical data
            historical_analysis = self._analyze_historical_performance(organization_id, historical_years)
            
            if not historical_analysis:
                logger.error("Unable to analyze historical data")
                return {}
            
            # Build projections for each scenario
            projections = {}
            
            for scenario in scenarios:
                logger.info(f"Building {scenario} scenario projections")
                projections[scenario] = self._build_scenario_projections(
                    historical_analysis, projection_years, scenario
                )
            
            return projections
            
        except Exception as e:
            logger.error(f"Error building comprehensive projections: {e}")
            return {}
    
    def _analyze_historical_performance(self, organization_id: int, years: int) -> Dict[str, Any]:
        """
        Analyze historical financial performance to identify trends and patterns.
        """
        try:
            current_date = datetime.now()
            historical_periods = []
            
            # Get quarterly data for the specified years
            for quarter in range(years * 4):
                period_end = current_date - timedelta(days=quarter * 90)
                period_start = period_end - timedelta(days=90)
                
                # Generate financial statements for this period
                income_gen = IncomeStatementGenerator(organization_id)
                income_stmt = income_gen.generate_income_statement(period_start, period_end)
                
                # Get SaaS KPIs if applicable
                saas_calculator = SaaSKPICalculator(self.db)
                saas_kpis = saas_calculator.calculate_comprehensive_kpis(
                    organization_id, period_start, period_end
                )
                
                period_data = {
                    'period': f"Q{4 - (quarter % 4)} {period_end.year}",
                    'period_start': period_start,
                    'period_end': period_end,
                    'financial_data': income_stmt.get('totals', {}),
                    'saas_kpis': saas_kpis.get('kpis', {}),
                    'quarter': quarter
                }
                
                historical_periods.append(period_data)
            
            # Analyze trends and patterns
            analysis = self._calculate_growth_trends(historical_periods)
            analysis['periods'] = historical_periods
            analysis['organization_id'] = organization_id
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing historical performance: {e}")
            return {}
    
    def _calculate_growth_trends(self, periods: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate growth trends from historical data.
        """
        try:
            if len(periods) < 2:
                return {}
            
            # Extract key metrics over time
            revenues = []
            operating_expenses = []
            gross_profits = []
            mrr_values = []
            customer_counts = []
            
            # Sort periods chronologically (reverse the list since it's built backwards)
            sorted_periods = sorted(periods, key=lambda x: x['period_start'])
            
            for period in sorted_periods:
                financial_data = period.get('financial_data', {})
                saas_data = period.get('saas_kpis', {})
                
                revenues.append(financial_data.get('total_revenue', 0))
                operating_expenses.append(financial_data.get('total_operating_expenses', 0))
                gross_profits.append(financial_data.get('gross_profit', 0))
                
                # SaaS metrics if available
                revenue_metrics = saas_data.get('revenue_metrics', {})
                customer_metrics = saas_data.get('customer_metrics', {})
                
                mrr_values.append(revenue_metrics.get('monthly_recurring_revenue', 0))
                customer_counts.append(customer_metrics.get('active_customers', 0))
            
            # Calculate growth rates and trends
            revenue_growth = self._calculate_growth_rate_trend(revenues)
            expense_growth = self._calculate_growth_rate_trend(operating_expenses)
            margin_trend = self._calculate_margin_trends(revenues, gross_profits)
            
            # SaaS-specific trends
            saas_trends = {}
            if any(mrr > 0 for mrr in mrr_values):
                saas_trends = {
                    'mrr_growth': self._calculate_growth_rate_trend(mrr_values),
                    'customer_growth': self._calculate_growth_rate_trend(customer_counts),
                    'is_saas_business': True,
                }
            else:
                saas_trends['is_saas_business'] = False
            
            # Business model classification
            business_model = self._classify_business_model(revenues, mrr_values)
            
            # Seasonal patterns
            seasonality = self._detect_seasonality(sorted_periods)
            
            return {
                'revenue_growth': revenue_growth,
                'expense_growth': expense_growth,
                'margin_trends': margin_trend,
                'saas_trends': saas_trends,
                'business_model': business_model,
                'seasonality': seasonality,
                'data_quality': len(sorted_periods),
                'analysis_date': datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Error calculating growth trends: {e}")
            return {}
    
    def _calculate_growth_rate_trend(self, values: List[float]) -> Dict[str, Any]:
        """
        Calculate growth rate trends from a series of values.
        """
        try:
            if len(values) < 2:
                return {'trend': 'insufficient_data', 'average_rate': 0}
            
            # Filter out zero values
            non_zero_values = [(i, v) for i, v in enumerate(values) if v > 0]
            
            if len(non_zero_values) < 2:
                return {'trend': 'insufficient_data', 'average_rate': 0}
            
            # Calculate quarter-over-quarter growth rates
            growth_rates = []
            for i in range(1, len(non_zero_values)):
                prev_idx, prev_val = non_zero_values[i-1]
                curr_idx, curr_val = non_zero_values[i]
                
                if prev_val > 0:
                    growth_rate = ((curr_val - prev_val) / prev_val) * 100
                    growth_rates.append(growth_rate)
            
            if not growth_rates:
                return {'trend': 'insufficient_data', 'average_rate': 0}
            
            # Calculate statistics
            avg_growth = sum(growth_rates) / len(growth_rates)
            volatility = np.std(growth_rates) if len(growth_rates) > 1 else 0
            
            # Determine trend direction
            if avg_growth > 5:
                trend = 'growing'
            elif avg_growth < -5:
                trend = 'declining'
            else:
                trend = 'stable'
            
            # Calculate compound annual growth rate (CAGR) if we have enough data
            cagr = 0
            if len(non_zero_values) >= 4:  # At least 4 quarters
                first_val = non_zero_values[0][1]
                last_val = non_zero_values[-1][1]
                periods = len(non_zero_values) - 1
                
                if first_val > 0:
                    cagr = ((last_val / first_val) ** (1/periods) - 1) * 100
            
            return {
                'trend': trend,
                'average_rate': avg_growth,
                'volatility': volatility,
                'cagr': cagr,
                'data_points': len(growth_rates),
                'latest_value': non_zero_values[-1][1],
                'growth_acceleration': growth_rates[-1] - growth_rates[0] if len(growth_rates) > 1 else 0
            }
            
        except Exception as e:
            logger.error(f"Error calculating growth rate trend: {e}")
            return {'trend': 'error', 'average_rate': 0}
    
    def _calculate_margin_trends(self, revenues: List[float], gross_profits: List[float]) -> Dict[str, Any]:
        """
        Calculate margin trends over time.
        """
        try:
            margins = []
            for i in range(len(revenues)):
                if revenues[i] > 0 and i < len(gross_profits):
                    margin = (gross_profits[i] / revenues[i]) * 100
                    margins.append(margin)
            
            if not margins:
                return {'trend': 'insufficient_data', 'average_margin': 0}
            
            avg_margin = sum(margins) / len(margins)
            margin_trend = self._calculate_growth_rate_trend(margins)
            
            return {
                'average_margin': avg_margin,
                'margin_trend': margin_trend['trend'],
                'margin_volatility': margin_trend['volatility'],
                'latest_margin': margins[-1] if margins else 0,
                'margin_improvement': margins[-1] - margins[0] if len(margins) > 1 else 0
            }
            
        except Exception as e:
            logger.error(f"Error calculating margin trends: {e}")
            return {'trend': 'error', 'average_margin': 0}
    
    def _classify_business_model(self, revenues: List[float], mrr_values: List[float]) -> Dict[str, Any]:
        """
        Classify the business model based on revenue patterns.
        """
        try:
            # Check if it's a SaaS business
            recurring_ratio = 0
            if any(mrr > 0 for mrr in mrr_values) and any(rev > 0 for rev in revenues):
                # Calculate average recurring vs total revenue
                avg_mrr = sum(mrr for mrr in mrr_values if mrr > 0) / len([mrr for mrr in mrr_values if mrr > 0])
                avg_revenue = sum(rev for rev in revenues if rev > 0) / len([rev for rev in revenues if rev > 0])
                
                if avg_revenue > 0:
                    recurring_ratio = (avg_mrr * 3) / avg_revenue  # MRR * 3 vs quarterly revenue
            
            # Classify business model
            if recurring_ratio > 0.7:
                model_type = 'saas'
                predictability = 'high'
            elif recurring_ratio > 0.3:
                model_type = 'hybrid'
                predictability = 'medium'
            else:
                model_type = 'transactional'
                predictability = 'low'
            
            # Calculate revenue consistency
            revenue_volatility = np.std(revenues) / np.mean(revenues) if revenues and np.mean(revenues) > 0 else 1
            
            return {
                'model_type': model_type,
                'predictability': predictability,
                'recurring_ratio': recurring_ratio,
                'revenue_volatility': revenue_volatility,
                'consistency_score': max(0, min(100, (1 - revenue_volatility) * 100))
            }
            
        except Exception as e:
            logger.error(f"Error classifying business model: {e}")
            return {'model_type': 'unknown', 'predictability': 'low'}
    
    def _detect_seasonality(self, periods: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Detect seasonal patterns in the business.
        """
        try:
            if len(periods) < 8:  # Need at least 2 years of quarterly data
                return {'has_seasonality': False}
            
            # Group by quarter
            quarterly_revenues = {1: [], 2: [], 3: [], 4: []}
            
            for period in periods:
                quarter = ((period['period_end'].month - 1) // 3) + 1
                revenue = period.get('financial_data', {}).get('total_revenue', 0)
                if revenue > 0:
                    quarterly_revenues[quarter].append(revenue)
            
            # Calculate seasonal factors
            overall_avg = np.mean([rev for quarter_revs in quarterly_revenues.values() for rev in quarter_revs])
            seasonal_factors = {}
            
            for quarter, revenues in quarterly_revenues.items():
                if revenues and overall_avg > 0:
                    quarter_avg = np.mean(revenues)
                    seasonal_factors[f'Q{quarter}'] = quarter_avg / overall_avg
            
            # Determine if there's significant seasonality
            if seasonal_factors:
                factor_values = list(seasonal_factors.values())
                seasonality_score = np.std(factor_values) if len(factor_values) > 1 else 0
                has_seasonality = seasonality_score > 0.15  # 15% variation threshold
            else:
                has_seasonality = False
                seasonality_score = 0
            
            return {
                'has_seasonality': has_seasonality,
                'seasonality_score': seasonality_score,
                'seasonal_factors': seasonal_factors,
                'strongest_quarter': max(seasonal_factors.keys(), key=lambda x: seasonal_factors[x]) if seasonal_factors else None
            }
            
        except Exception as e:
            logger.error(f"Error detecting seasonality: {e}")
            return {'has_seasonality': False}
    
    def _build_scenario_projections(self, 
                                  historical_analysis: Dict[str, Any], 
                                  years: int, 
                                  scenario: str) -> List[Dict[str, Any]]:
        """
        Build projections for a specific scenario based on historical analysis.
        """
        try:
            # Get base growth assumptions for scenario
            scenario_assumptions = self._get_scenario_assumptions(historical_analysis, scenario)
            
            # Get latest financial position
            latest_period = historical_analysis['periods'][0] if historical_analysis['periods'] else {}
            base_financial = latest_period.get('financial_data', {})
            
            # Starting values
            base_revenue = base_financial.get('total_revenue', 0) * 4  # Annualize quarterly data
            base_operating_expenses = base_financial.get('total_operating_expenses', 0) * 4
            base_gross_profit = base_financial.get('gross_profit', 0) * 4
            
            # Build year-over-year projections
            projections = []
            
            for year in range(1, years + 1):
                # Apply growth rates with scenario adjustments
                revenue_growth = scenario_assumptions['revenue_growth_rate']
                expense_growth = scenario_assumptions['expense_growth_rate']
                
                # Apply seasonal adjustments if detected
                seasonal_factor = 1.0
                if historical_analysis.get('seasonality', {}).get('has_seasonality'):
                    # Apply seasonal patterns (simplified for annual projections)
                    seasonal_factor = 1.0  # Would be more complex for monthly projections
                
                # Calculate projected values
                projected_revenue = base_revenue * ((1 + revenue_growth) ** year) * seasonal_factor
                projected_expenses = base_operating_expenses * ((1 + expense_growth) ** year)
                projected_gross_profit = projected_revenue * scenario_assumptions['gross_margin']
                
                # Calculate EBITDA and other metrics
                projected_ebitda = projected_gross_profit - projected_expenses
                
                # Depreciation (as % of revenue or based on historical patterns)
                depreciation = projected_revenue * scenario_assumptions['depreciation_rate']
                
                # EBIT and taxes
                ebit = projected_ebitda - depreciation
                taxes = max(0, ebit * scenario_assumptions['tax_rate']) if ebit > 0 else 0
                nopat = ebit - taxes
                
                # Working capital and CapEx
                working_capital_change = (projected_revenue - (base_revenue * ((1 + revenue_growth) ** (year - 1)) if year > 1 else 0)) * scenario_assumptions['working_capital_rate']
                capex = projected_revenue * scenario_assumptions['capex_rate']
                
                # Free Cash Flow
                free_cash_flow = nopat + depreciation - capex - working_capital_change
                
                # SaaS-specific metrics if applicable
                saas_metrics = {}
                if historical_analysis.get('saas_trends', {}).get('is_saas_business'):
                    # Project SaaS metrics
                    mrr_growth = scenario_assumptions.get('mrr_growth_rate', revenue_growth / 12)
                    base_mrr = latest_period.get('saas_kpis', {}).get('revenue_metrics', {}).get('monthly_recurring_revenue', projected_revenue / 12)
                    
                    projected_mrr = base_mrr * ((1 + mrr_growth) ** (year * 12))
                    projected_arr = projected_mrr * 12
                    
                    saas_metrics = {
                        'projected_mrr': projected_mrr,
                        'projected_arr': projected_arr,
                        'arr_growth_rate': mrr_growth * 12,
                    }
                
                projection = {
                    'year': year,
                    'scenario': scenario,
                    'revenue': projected_revenue,
                    'gross_profit': projected_gross_profit,
                    'operating_expenses': projected_expenses,
                    'ebitda': projected_ebitda,
                    'depreciation': depreciation,
                    'ebit': ebit,
                    'taxes': taxes,
                    'nopat': nopat,
                    'capex': capex,
                    'working_capital_change': working_capital_change,
                    'free_cash_flow': free_cash_flow,
                    'tax_rate': scenario_assumptions['tax_rate'],
                    'revenue_growth_rate': revenue_growth,
                    'gross_margin': scenario_assumptions['gross_margin'],
                    **saas_metrics
                }
                
                projections.append(projection)
            
            return projections
            
        except Exception as e:
            logger.error(f"Error building scenario projections: {e}")
            return []
    
    def _get_scenario_assumptions(self, historical_analysis: Dict[str, Any], scenario: str) -> Dict[str, float]:
        """
        Get growth and financial assumptions for different scenarios.
        """
        try:
            # Extract historical trends
            revenue_trend = historical_analysis.get('revenue_growth', {})
            expense_trend = historical_analysis.get('expense_growth', {})
            margin_trends = historical_analysis.get('margin_trends', {})
            saas_trends = historical_analysis.get('saas_trends', {})
            
            # Base assumptions from historical data
            base_revenue_growth = max(0.02, min(0.50, revenue_trend.get('average_rate', 15) / 100))  # Cap between 2-50%
            base_expense_growth = max(0.0, min(0.40, expense_trend.get('average_rate', 12) / 100))
            base_gross_margin = max(0.2, min(0.9, margin_trends.get('average_margin', 60) / 100))
            
            # SaaS-specific adjustments
            if saas_trends.get('is_saas_business'):
                mrr_growth = max(0.02, min(0.30, saas_trends.get('mrr_growth', {}).get('average_rate', 20) / 100))
            else:
                mrr_growth = 0
            
            # Scenario multipliers
            if scenario == 'upside':
                revenue_multiplier = 1.5
                expense_multiplier = 0.9  # Better operational efficiency
                margin_multiplier = 1.1
            elif scenario == 'downside':
                revenue_multiplier = 0.6
                expense_multiplier = 1.1  # Higher costs
                margin_multiplier = 0.9
            else:  # base
                revenue_multiplier = 1.0
                expense_multiplier = 1.0
                margin_multiplier = 1.0
            
            return {
                'revenue_growth_rate': base_revenue_growth * revenue_multiplier,
                'expense_growth_rate': base_expense_growth * expense_multiplier,
                'gross_margin': base_gross_margin * margin_multiplier,
                'mrr_growth_rate': mrr_growth * revenue_multiplier if mrr_growth > 0 else 0,
                'tax_rate': 0.25,  # Standard corporate tax rate
                'depreciation_rate': 0.02,  # 2% of revenue
                'capex_rate': 0.03,  # 3% of revenue
                'working_capital_rate': 0.02,  # 2% of revenue growth
            }
            
        except Exception as e:
            logger.error(f"Error getting scenario assumptions: {e}")
            # Return conservative defaults
            return {
                'revenue_growth_rate': 0.10,
                'expense_growth_rate': 0.08,
                'gross_margin': 0.60,
                'tax_rate': 0.25,
                'depreciation_rate': 0.02,
                'capex_rate': 0.03,
                'working_capital_rate': 0.02,
            }